@page "/profile"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Components.Authorization
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using System.ComponentModel.DataAnnotations

@inherits LocalizedComponentBase
@inject IEmployeeAuthenticationService EmployeeAuthService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>@GetPageTitle("Profile", "الملف الشخصي")</PageTitle>

<AuthenticationGuard>
<div class="container py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/dashboard" class="text-decoration-none">
                    <i class="fas fa-home @GetMarginEnd(1)"></i>
                    @L("Dashboard", "لوحة التحكم")
                </a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                @L("Profile", "الملف الشخصي")
            </li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex align-items-center">
                <div class="@GetMarginEnd(3)">
                    <i class="fas fa-user-circle fa-3x text-primary"></i>
                </div>
                <div>
                    <h1 class="h3 mb-1">@L("My Profile", "ملفي الشخصي")</h1>
                    <p class="text-muted mb-0">@L("Manage your account information", "إدارة معلومات حسابك")</p>
                </div>
            </div>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 text-muted">@L("Loading profile...", "جاري تحميل الملف الشخصي...")</p>
        </div>
    }
    else if (currentUser != null)
    {
        <div class="row">
            <!-- Profile Information Card -->
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user @GetMarginEnd(2)"></i>
                            @L("Profile Information", "معلومات الملف الشخصي")
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(successMessage))
                        {
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle @GetMarginEnd(1)"></i>
                                @successMessage
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-triangle @GetMarginEnd(1)"></i>
                                @errorMessage
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        }

                        <EditForm Model="profileModel" OnValidSubmit="UpdateProfile">
                            <DataAnnotationsValidator />

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="employeeId" class="form-label">
                                        <i class="fas fa-id-card @GetMarginEnd(1)"></i>
                                        @L("Employee ID", "رقم الموظف")
                                    </label>
                                    <input type="text" class="form-control" id="employeeId" 
                                           value="@currentUser.EmployeeId" readonly />
                                    <div class="form-text">@L("Employee ID cannot be changed", "لا يمكن تغيير رقم الموظف")</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope @GetMarginEnd(1)"></i>
                                        @L("Email Address", "البريد الإلكتروني")
                                    </label>
                                    <InputText @bind-Value="profileModel.Email" class="form-control" id="email" />
                                    <ValidationMessage For="@(() => profileModel.Email)" />
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="englishName" class="form-label">
                                        <i class="fas fa-user @GetMarginEnd(1)"></i>
                                        @L("English Name", "الاسم بالإنجليزية")
                                    </label>
                                    <InputText @bind-Value="profileModel.EnglishName" class="form-control" id="englishName" />
                                    <ValidationMessage For="@(() => profileModel.EnglishName)" />
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="arabicName" class="form-label">
                                        <i class="fas fa-user @GetMarginEnd(1)"></i>
                                        @L("Arabic Name", "الاسم بالعربية")
                                    </label>
                                    <InputText @bind-Value="profileModel.ArabicName" class="form-control" id="arabicName" />
                                    <ValidationMessage For="@(() => profileModel.ArabicName)" />
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="preferredLanguage" class="form-label">
                                        <i class="fas fa-language @GetMarginEnd(1)"></i>
                                        @L("Preferred Language", "اللغة المفضلة")
                                    </label>
                                    <InputSelect @bind-Value="profileModel.PreferredLanguage" class="form-select" id="preferredLanguage">
                                        <option value="en">English</option>
                                        <option value="ar">العربية</option>
                                    </InputSelect>
                                    <ValidationMessage For="@(() => profileModel.PreferredLanguage)" />
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="role" class="form-label">
                                        <i class="fas fa-user-tag @GetMarginEnd(1)"></i>
                                        @L("Role", "الدور")
                                    </label>
                                    <input type="text" class="form-control" id="role" 
                                           value="@GetRoleDisplayName(currentUser.Role)" readonly />
                                    <div class="form-text">@L("Role is assigned by administrators", "الدور محدد من قبل المديرين")</div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary" disabled="@isUpdating">
                                    @if (isUpdating)
                                    {
                                        <span class="spinner-border spinner-border-sm @GetMarginEnd(1)" role="status"></span>
                                    }
                                    <i class="fas fa-save @GetMarginEnd(1)"></i>
                                    @L("Update Profile", "تحديث الملف الشخصي")
                                </button>

                                <a href="/profile/change-password" class="btn btn-outline-secondary">
                                    <i class="fas fa-key @GetMarginEnd(1)"></i>
                                    @L("Change Password", "تغيير كلمة المرور")
                                </a>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>

            <!-- Profile Summary Card -->
            <div class="col-lg-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle @GetMarginEnd(1)"></i>
                            @L("Account Summary", "ملخص الحساب")
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <small class="text-muted">@L("Member Since", "عضو منذ")</small>
                            <div class="fw-bold">@currentUser.CreatedAt.ToString("MMMM yyyy")</div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">@L("Last Updated", "آخر تحديث")</small>
                            <div class="fw-bold">
                                @currentUser.UpdatedAt.ToString("dd/MM/yyyy")
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">@L("Account Status", "حالة الحساب")</small>
                            <div>
                                @if (currentUser.IsActive)
                                {
                                    <span class="badge bg-success">
                                        <i class="fas fa-check @GetMarginEnd(1)"></i>
                                        @L("Active", "نشط")
                                    </span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">
                                        <i class="fas fa-times @GetMarginEnd(1)"></i>
                                        @L("Inactive", "غير نشط")
                                    </span>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="alert alert-warning" role="alert">
            <i class="fas fa-exclamation-triangle @GetMarginEnd(1)"></i>
            @L("Unable to load profile information.", "تعذر تحميل معلومات الملف الشخصي.")
        </div>
    }
</div>
</AuthenticationGuard>

@code {
    private ApplicationUser? currentUser;
    private ProfileModel profileModel = new();
    private bool isLoading = true;
    private bool isUpdating = false;
    private string successMessage = string.Empty;
    private string errorMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await LoadUserProfile();
    }

    private async Task LoadUserProfile()
    {
        try
        {
            isLoading = true;
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var employeeId = authState.User.FindFirst("EmployeeId")?.Value;
                if (!string.IsNullOrEmpty(employeeId))
                {
                    currentUser = await EmployeeAuthService.FindByEmployeeIdAsync(employeeId);
                    if (currentUser != null)
                    {
                        profileModel = new ProfileModel
                        {
                            Email = currentUser.Email ?? string.Empty,
                            EnglishName = currentUser.EnglishName,
                            ArabicName = currentUser.ArabicName,
                            PreferredLanguage = currentUser.PreferredLanguage
                        };
                    }
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = L("Error loading profile.", "خطأ في تحميل الملف الشخصي.");
            Console.WriteLine($"Profile load error: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task UpdateProfile()
    {
        try
        {
            isUpdating = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;

            if (currentUser != null)
            {
                // Update user properties
                currentUser.Email = profileModel.Email;
                currentUser.EnglishName = profileModel.EnglishName;
                currentUser.ArabicName = profileModel.ArabicName;
                currentUser.PreferredLanguage = profileModel.PreferredLanguage;

                // Here you would call a service to update the user
                // For now, we'll simulate success
                await Task.Delay(1000); // Simulate API call

                successMessage = L("Profile updated successfully!", "تم تحديث الملف الشخصي بنجاح!");
            }
        }
        catch (Exception ex)
        {
            errorMessage = L("Error updating profile.", "خطأ في تحديث الملف الشخصي.");
            Console.WriteLine($"Profile update error: {ex.Message}");
        }
        finally
        {
            isUpdating = false;
        }
    }

    private string GetRoleDisplayName(UserRole role)
    {
        return role switch
        {
            UserRole.SUPER_ADMIN => L("Super Administrator", "مدير عام"),
            UserRole.MANAGER => L("Manager", "مدير"),
            UserRole.SUPERVISOR => L("Supervisor", "مشرف"),
            UserRole.EXCELLENCE_TEAM => L("Excellence Team", "فريق التميز"),
            UserRole.EMPLOYEE => L("Employee", "موظف"),
            _ => role.ToString()
        };
    }

    public class ProfileModel
    {
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Invalid email format")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "English name is required")]
        [StringLength(100, ErrorMessage = "English name cannot exceed 100 characters")]
        public string EnglishName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Arabic name is required")]
        [StringLength(100, ErrorMessage = "Arabic name cannot exceed 100 characters")]
        public string ArabicName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Preferred language is required")]
        public string PreferredLanguage { get; set; } = "en";
    }
}
