@page "/evaluations"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using Microsoft.EntityFrameworkCore
@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject IEvaluationService EvaluationService
@inject IJSRuntime JSRuntime

<PageTitle>@L("Evaluations", "التقييمات")</PageTitle>

<!-- Page Header -->
<div class="page-header @GetLayoutClass()">
    <!-- Breadcrumb -->
    <nav aria-label="@L("Breadcrumb", "مسار التنقل")">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/" class="text-decoration-none">@L("Home", "الرئيسية")</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                @L("Evaluations", "التقييمات")
            </li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-start flex-wrap">
        <div class="flex-grow-1">
            <h1 class="h1 mb-2">
                <i class="fas fa-chart-line @GetMarginEnd() text-primary"></i>
                @L("Evaluations", "التقييمات")
            </h1>
            <p class="lead mb-0">@L("Comprehensive employee performance evaluation system", "نظام التقييم الشامل لأداء الموظفين")</p>
        </div>

        <div class="@GetMarginStart() mt-2 mt-md-0">
            <div class="d-flex gap-2 @GetLayoutClass()">
                <a href="/evaluations/comprehensive" class="btn btn-primary">
                    <i class="fas fa-calculator @GetMarginEnd(1)"></i>
                    @L("Comprehensive Evaluation", "التقييم الشامل")
                </a>
                <button class="btn btn-outline-secondary" @onclick="ExportEvaluations">
                    <i class="fas fa-download @GetMarginEnd(1)"></i>
                    @L("Export", "تصدير")
                </button>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@totalEvaluations</div>
                        <div class="stat-label">@L("Total Evaluations", "إجمالي التقييمات")</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@pendingEvaluations</div>
                        <div class="stat-label">@L("Pending", "في الانتظار")</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@approvedEvaluations</div>
                        <div class="stat-label">@L("Approved", "معتمد")</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex align-items-center">
                    <div class="stat-icon">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="@GetMarginStart()">
                        <div class="stat-value">@averageScore.ToString("F1")%</div>
                        <div class="stat-label">@L("Average Score", "المتوسط")</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="container-fluid">
    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">@L("Search", "البحث")</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" @bind="searchTerm" @bind:event="oninput" @bind:after="FilterEvaluations"
                               placeholder="@L("Search by employee name, ID, or department...", "البحث باسم الموظف أو الرقم أو القسم...")" />
                    </div>
                </div>
                <div class="col-md-3">
                    <label class="form-label">@L("Status", "الحالة")</label>
                    <select class="form-select" @bind="statusFilter" @bind:after="FilterEvaluations">
                        <option value="">@L("All Statuses", "جميع الحالات")</option>
                        <option value="DRAFT">@L("Draft", "مسودة")</option>
                        <option value="SUBMITTED">@L("Submitted", "مرسل")</option>
                        <option value="APPROVED">@L("Approved", "معتمد")</option>
                        <option value="REJECTED">@L("Rejected", "مرفوض")</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">@L("Department", "القسم")</label>
                    <select class="form-select" @bind="departmentFilter" @bind:after="FilterEvaluations">
                        <option value="">@L("All Departments", "جميع الأقسام")</option>
                        @foreach (var dept in departments)
                        {
                            <option value="@dept.Id">@(IsArabic ? dept.NameAr : dept.NameEn)</option>
                        }
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">@L("Date Range", "نطاق التاريخ")</label>
                    <select class="form-select" @bind="dateFilter" @bind:after="FilterEvaluations">
                        <option value="">@L("All Time", "كل الأوقات")</option>
                        <option value="today">@L("Today", "اليوم")</option>
                        <option value="week">@L("This Week", "هذا الأسبوع")</option>
                        <option value="month">@L("This Month", "هذا الشهر")</option>
                        <option value="quarter">@L("This Quarter", "هذا الربع")</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Evaluations Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <div class="d-flex justify-content-between align-items-center">
                <h6 class="card-title mb-0">
                    @L("Evaluation Records", "سجلات التقييم") (@filteredEvaluations.Count)
                </h6>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" @onclick="RefreshData">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            @if (isLoading)
            {
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">@L("Loading", "جاري التحميل")</span>
                    </div>
                    <p class="mt-2 text-muted">@L("Loading evaluations...", "جاري تحميل التقييمات...")</p>
                </div>
            }
            else if (!filteredEvaluations.Any())
            {
                <div class="text-center py-5">
                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">@L("No evaluations found", "لم يتم العثور على تقييمات")</h5>
                    <p class="text-muted">@L("Start by creating your first evaluation", "ابدأ بإنشاء أول تقييم")</p>
                    <a href="/evaluations/comprehensive" class="btn btn-primary">
                        <i class="fas fa-calculator @GetMarginEnd(1)"></i>
                        @L("Create Evaluation", "إنشاء تقييم")
                    </a>
                </div>
            }
            else
            {
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="@GetTextStart()">@L("Employee", "الموظف")</th>
                                <th class="@GetTextStart()">@L("Evaluator", "المقيم")</th>
                                <th class="@GetTextStart()">@L("Department", "القسم")</th>
                                <th class="text-center">@L("Score", "النتيجة")</th>
                                <th class="text-center">@L("Status", "الحالة")</th>
                                <th class="@GetTextStart()">@L("Date", "التاريخ")</th>
                                <th class="text-center">@L("Actions", "الإجراءات")</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var evaluation in GetPagedEvaluations())
                            {
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm @GetMarginEnd()">
                                                <div class="avatar-title bg-primary rounded-circle">
                                                    @GetUserInitials(evaluation.Employee)
                                                </div>
                                            </div>
                                            <div>
                                                <div class="fw-medium">@GetUserDisplayName(evaluation.Employee)</div>
                                                <small class="text-muted">@evaluation.Employee.EmployeeId</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="fw-medium">@GetUserDisplayName(evaluation.Evaluator)</div>
                                    </td>
                                    <td>
                                        @if (evaluation.Employee.PrimaryDepartment != null)
                                        {
                                            <span class="badge bg-light text-dark">
                                                @(IsArabic ? evaluation.Employee.PrimaryDepartment.NameAr : evaluation.Employee.PrimaryDepartment.NameEn)
                                            </span>
                                        }
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-@GetPerformanceClass(evaluation.TotalScore ?? 0) fs-6">
                                            @((evaluation.TotalScore ?? 0).ToString("F1"))%
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-@GetStatusBadgeClass(evaluation.Status)">
                                            @GetStatusDisplayName(evaluation.Status)
                                        </span>
                                    </td>
                                    <td>
                                        <div>@evaluation.CreatedAt.ToString("dd/MM/yyyy")</div>
                                        <small class="text-muted">@evaluation.CreatedAt.ToString("HH:mm")</small>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary" @onclick="() => ViewEvaluation(evaluation)"
                                                    title="@L("View", "عرض")">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            @if (evaluation.Status == EvaluationStatus.DRAFT)
                                            {
                                                <button class="btn btn-outline-warning" @onclick="() => EditEvaluation(evaluation)"
                                                        title="@L("Edit", "تعديل")">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            }
                                            @if (evaluation.Status == EvaluationStatus.SUBMITTED)
                                            {
                                                <button class="btn btn-outline-success" @onclick="() => ApproveEvaluation(evaluation)"
                                                        title="@L("Approve", "اعتماد")">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" @onclick="() => RejectEvaluation(evaluation)"
                                                        title="@L("Reject", "رفض")">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if (totalPages > 1)
                {
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="text-muted">
                                @L("Showing", "عرض") @((currentPage - 1) * pageSize + 1) @L("to", "إلى") @Math.Min(currentPage * pageSize, filteredEvaluations.Count) @L("of", "من") @filteredEvaluations.Count @L("entries", "إدخال")
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0">
                                    <li class="page-item @(currentPage == 1 ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(currentPage - 1)" disabled="@(currentPage == 1)">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                    </li>
                                    @for (int i = Math.Max(1, currentPage - 2); i <= Math.Min(totalPages, currentPage + 2); i++)
                                    {
                                        <li class="page-item @(i == currentPage ? "active" : "")">
                                            <button class="page-link" @onclick="() => ChangePage(i)">@i</button>
                                        </li>
                                    }
                                    <li class="page-item @(currentPage == totalPages ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => ChangePage(currentPage + 1)" disabled="@(currentPage == totalPages)">
                                            <i class="fas fa-chevron-right"></i>
                                        </button>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</div>

@code {
    private bool isLoading = true;
    private string searchTerm = "";
    private string statusFilter = "";
    private string departmentFilter = "";
    private string dateFilter = "";

    private int currentPage = 1;
    private int pageSize = 10;
    private int totalPages = 1;

    // Statistics
    private int totalEvaluations = 0;
    private int pendingEvaluations = 0;
    private int approvedEvaluations = 0;
    private decimal averageScore = 0;

    private List<Evaluation> allEvaluations = new();
    private List<Evaluation> filteredEvaluations = new();
    private List<Department> departments = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        StateHasChanged();

        try
        {
            // Load departments
            departments = await DbContext.Departments
                .Where(d => d.IsActive && !d.IsDeleted)
                .OrderBy(d => d.NameEn)
                .ToListAsync();

            // Load evaluations using service (includes proper soft delete filtering and optimization)
            allEvaluations = await EvaluationService.GetEvaluationsAsync();

            // Calculate statistics
            totalEvaluations = allEvaluations.Count;
            pendingEvaluations = allEvaluations.Count(e => e.Status == EvaluationStatus.SUBMITTED);
            approvedEvaluations = allEvaluations.Count(e => e.Status == EvaluationStatus.APPROVED);
            averageScore = allEvaluations.Any() ? (decimal)allEvaluations.Average(e => (double)(e.TotalScore ?? 0)) : 0;

            FilterEvaluations();
        }
        catch (Exception ex)
        {
            // Log error and show user-friendly message
            Console.WriteLine($"Error loading evaluation data: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", L("Error loading data. Please refresh the page.", "خطأ في تحميل البيانات. يرجى تحديث الصفحة."));
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private void FilterEvaluations()
    {
        filteredEvaluations = allEvaluations.ToList();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            filteredEvaluations = filteredEvaluations.Where(e =>
                // Employee name search (both Arabic and English)
                (!string.IsNullOrEmpty(e.Employee.EnglishName) && e.Employee.EnglishName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                (!string.IsNullOrEmpty(e.Employee.ArabicName) && e.Employee.ArabicName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                // Employee ID search
                (!string.IsNullOrEmpty(e.Employee.EmployeeId) && e.Employee.EmployeeId.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                // Department name search (both Arabic and English)
                (e.Employee.PrimaryDepartment != null && !string.IsNullOrEmpty(e.Employee.PrimaryDepartment.NameEn) && e.Employee.PrimaryDepartment.NameEn.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                (e.Employee.PrimaryDepartment != null && !string.IsNullOrEmpty(e.Employee.PrimaryDepartment.NameAr) && e.Employee.PrimaryDepartment.NameAr.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                // Evaluation period search (search in formatted date strings)
                (e.EvaluationPeriodStart.ToString("yyyy-MM").Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                (e.EvaluationPeriodStart.ToString("yyyy-MM-dd").Contains(searchTerm, StringComparison.OrdinalIgnoreCase)) ||
                // Status search
                e.Status.ToString().Contains(searchTerm, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        // Apply status filter
        if (!string.IsNullOrWhiteSpace(statusFilter) && Enum.TryParse<EvaluationStatus>(statusFilter, out var status))
        {
            filteredEvaluations = filteredEvaluations.Where(e => e.Status == status).ToList();
        }

        // Apply department filter
        if (!string.IsNullOrWhiteSpace(departmentFilter) && int.TryParse(departmentFilter, out var deptId))
        {
            filteredEvaluations = filteredEvaluations.Where(e => e.Employee.PrimaryDepartmentId == deptId).ToList();
        }

        // Apply date filter
        if (!string.IsNullOrWhiteSpace(dateFilter))
        {
            var now = DateTime.Now;
            filteredEvaluations = dateFilter switch
            {
                "today" => filteredEvaluations.Where(e => e.CreatedAt.Date == now.Date).ToList(),
                "week" => filteredEvaluations.Where(e => e.CreatedAt >= now.AddDays(-7)).ToList(),
                "month" => filteredEvaluations.Where(e => e.CreatedAt >= now.AddDays(-30)).ToList(),
                "quarter" => filteredEvaluations.Where(e => e.CreatedAt >= now.AddDays(-90)).ToList(),
                _ => filteredEvaluations
            };
        }

        // Update pagination
        totalPages = (int)Math.Ceiling((double)filteredEvaluations.Count / pageSize);
        currentPage = Math.Min(currentPage, Math.Max(1, totalPages));

        StateHasChanged();
    }

    private List<Evaluation> GetPagedEvaluations()
    {
        return filteredEvaluations
            .Skip((currentPage - 1) * pageSize)
            .Take(pageSize)
            .ToList();
    }

    private void ChangePage(int page)
    {
        if (page >= 1 && page <= totalPages)
        {
            currentPage = page;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private async Task ExportEvaluations()
    {
        await JSRuntime.InvokeVoidAsync("alert", L("Export functionality will be implemented soon", "سيتم تنفيذ وظيفة التصدير قريباً"));
    }

    private async Task ViewEvaluation(Evaluation evaluation)
    {
        // Navigate to evaluation details page
        await JSRuntime.InvokeVoidAsync("alert", L("View evaluation functionality will be implemented", "سيتم تنفيذ وظيفة عرض التقييم"));
    }

    private async Task EditEvaluation(Evaluation evaluation)
    {
        // Navigate to edit evaluation page
        await JSRuntime.InvokeVoidAsync("alert", L("Edit evaluation functionality will be implemented", "سيتم تنفيذ وظيفة تعديل التقييم"));
    }

    private async Task ApproveEvaluation(Evaluation evaluation)
    {
        if (await JSRuntime.InvokeAsync<bool>("confirm", L("Are you sure you want to approve this evaluation?", "هل أنت متأكد من اعتماد هذا التقييم؟")))
        {
            try
            {
                // TODO: Get current user ID from authentication context
                var success = await EvaluationService.ApproveEvaluationAsync(evaluation.Id, "current-user-id");
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", L("Evaluation approved successfully", "تم اعتماد التقييم بنجاح"));
                    await RefreshData();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", L("Failed to approve evaluation", "فشل في اعتماد التقييم"));
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", L("Error approving evaluation", "خطأ في اعتماد التقييم"));
            }
        }
    }

    private async Task RejectEvaluation(Evaluation evaluation)
    {
        var reason = await JSRuntime.InvokeAsync<string>("prompt", L("Please provide a reason for rejection:", "يرجى تقديم سبب الرفض:"));
        if (!string.IsNullOrWhiteSpace(reason))
        {
            try
            {
                // TODO: Get current user ID from authentication context
                var success = await EvaluationService.RejectEvaluationAsync(evaluation.Id, "current-user-id", reason);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", L("Evaluation rejected successfully", "تم رفض التقييم بنجاح"));
                    await RefreshData();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", L("Failed to reject evaluation", "فشل في رفض التقييم"));
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("alert", L("Error rejecting evaluation", "خطأ في رفض التقييم"));
            }
        }
    }

    // Utility methods
    private string GetUserDisplayName(ApplicationUser user)
    {
        return IsArabic ? user.ArabicName : user.EnglishName;
    }

    private string GetUserInitials(ApplicationUser user)
    {
        var name = GetUserDisplayName(user);
        var parts = name.Split(' ', StringSplitOptions.RemoveEmptyEntries);

        if (parts.Length >= 2)
        {
            return $"{parts[0][0]}{parts[1][0]}".ToUpper();
        }
        else if (parts.Length == 1)
        {
            return parts[0].Length >= 2 ? parts[0].Substring(0, 2).ToUpper() : parts[0].ToUpper();
        }

        return "??";
    }

    private string GetPerformanceClass(decimal score)
    {
        return score >= 90 ? "success" : score >= 80 ? "info" : score >= 70 ? "warning" : "danger";
    }

    private string GetStatusBadgeClass(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.DRAFT => "secondary",
            EvaluationStatus.SUBMITTED => "warning",
            EvaluationStatus.APPROVED => "success",
            EvaluationStatus.REJECTED => "danger",
            _ => "secondary"
        };
    }

    private string GetStatusDisplayName(EvaluationStatus status)
    {
        return status switch
        {
            EvaluationStatus.DRAFT => L("Draft", "مسودة"),
            EvaluationStatus.SUBMITTED => L("Submitted", "مرسل"),
            EvaluationStatus.APPROVED => L("Approved", "معتمد"),
            EvaluationStatus.REJECTED => L("Rejected", "مرفوض"),
            _ => status.ToString()
        };
    }
}