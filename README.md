# Employee Rating System (نظام تقييم الموظفين)

A comprehensive bilingual (Arabic/English) employee performance evaluation system with hierarchical department structure and role-based access control.

## 🌟 Features

### 🏢 Hierarchical Department Structure
- **Unlimited Nesting**: Support for unlimited department hierarchy levels
- **Many-to-Many Relationships**: Employees can belong to multiple departments
- **Dynamic Restructuring**: Real-time organizational changes without data loss
- **Visual Hierarchy**: Interactive organizational chart with role-based views

### 👥 Role-Based Access Control
- **Super Admin**: Complete system administration and configuration
- **Manager**: Department-level performance management and oversight
- **Supervisor**: Direct team management and employee evaluation
- **Quality Team**: Quality assurance and evaluation oversight (read-only)
- **Employee**: Personal performance tracking and development

### 📊 Configurable Evaluation System
- **Dynamic Categories**: Super Admin can add/remove evaluation categories
- **Bilingual Support**: All content available in Arabic and English
- **Weighted Scoring**: Configurable percentage weights for each category
- **Default Categories**:
  - Attendance & Punctuality (الحضور والانصراف)
  - Work Volume (حجم العمل)
  - Creative Work (العمل الإبداعي)
  - Direct Supervisor Evaluation (تقييم المسؤول المباشر)

### 🔒 Security & Access Control
- **Strict Boundaries**: No cross-department access outside assigned hierarchy
- **Hierarchical Inheritance**: Access to all subordinate levels
- **Audit Trail**: Complete tracking of all system changes
- **Data Protection**: Role-based data visibility and modification rights

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Django 4.2+
- PostgreSQL (production) or SQLite (development)
- Redis (for caching and sessions)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd employee_rating
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements-dev.txt
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

5. **Database setup**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   ```

6. **Create superuser**
   ```bash
   python manage.py createsuperuser
   ```

7. **Run development server**
   c# blazor
   ```

## 🗄️ Database Configuration

The system supports multiple database backends through environment variables:

### SQLite (Default - Development)
```env
DATABASE_ENGINE=sqlite
```

### PostgreSQL (Recommended for Production)
```env
DATABASE_ENGINE=postgresql
POSTGRES_DB=employee_rating
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
```

### Oracle (Enterprise)
```env
DATABASE_ENGINE=oracle
ORACLE_DB_NAME=xe
ORACLE_USER=employee_rating
ORACLE_PASSWORD=your_password
ORACLE_HOST=localhost
ORACLE_PORT=1521
```

## 🌐 API Documentation

The system provides a comprehensive REST API with automatic documentation:

- **Swagger UI**: `/api/v1/docs/`
- **ReDoc**: `/api/v1/redoc/`
- **OpenAPI Schema**: `/api/v1/schema/`

### Key API Endpoints

```
GET    /api/v1/departments/           # List departments
POST   /api/v1/departments/           # Create department (Super Admin)
GET    /api/v1/departments/{id}/      # Department details
GET    /api/v1/departments/hierarchy/ # Department hierarchy tree

GET    /api/v1/users/                 # List users
POST   /api/v1/users/                 # Create user (Super Admin)
GET    /api/v1/users/{id}/            # User details
GET    /api/v1/users/{id}/evaluations/ # User evaluations

GET    /api/v1/evaluations/           # List evaluations
POST   /api/v1/evaluations/           # Create evaluation
GET    /api/v1/evaluations/{id}/      # Evaluation details
POST   /api/v1/evaluations/{id}/submit/ # Submit evaluation
POST   /api/v1/evaluations/{id}/approve/ # Approve evaluation

GET    /api/v1/evaluation-categories/ # List categories
POST   /api/v1/evaluation-categories/ # Create category (Super Admin)
GET    /api/v1/evaluation-questions/  # List questions
POST   /api/v1/evaluation-questions/  # Create question (Super Admin)
```

## 🏗️ Project Structure

```
employee_rating/
├── employee_rating/          # Main project settings
│   ├── settings/            # Environment-specific settings
│   │   ├── base.py         # Base settings
│   │   ├── development.py  # Development settings
│   │   ├── staging.py      # Staging settings
│   │   └── production.py   # Production settings
│   ├── urls.py             # Main URL configuration
│   ├── wsgi.py             # WSGI configuration
│   └── asgi.py             # ASGI configuration
├── core/                    # Core functionality and base models
├── users/                   # User management and authentication
├── departments/             # Department hierarchy management
├── evaluations/             # Evaluation system
├── api/                     # REST API implementation
├── templates/               # HTML templates
├── static/                  # Static files (CSS, JS, images)
├── locale/                  # Translation files
├── requirements.txt         # Production dependencies
├── requirements-dev.txt     # Development dependencies
├── requirements-prod.txt    # Production-specific dependencies
├── .env.example            # Environment variables template
└── manage.py               # Django management script
```

## 🌍 Internationalization

The system supports full bilingual functionality:

### Supported Languages
- **English** (en)
- **Arabic** (العربية) (ar)

### Language Features
- **RTL/LTR Support**: Proper text direction handling
- **Dynamic Switching**: Real-time language switching
- **Cultural Formats**: Support for both Gregorian and Hijri calendars
- **Localized Content**: All interface elements, reports, and data

### Adding Translations
```bash
# Generate translation files
python manage.py makemessages -l ar
python manage.py makemessages -l en

# Compile translations
python manage.py compilemessages
```

## 🔧 Development

### Running Tests
```bash
pytest
```

### Code Quality
```bash
# Format code
black .
isort .

# Lint code
flake8
```

### Database Migrations
```bash
# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Show migration status
python manage.py showmigrations
```

## 🚀 Deployment

### Production Checklist
- [ ] Set `DEBUG=False` in production settings
- [ ] Configure proper database (PostgreSQL/Oracle)
- [ ] Set up Redis for caching and sessions
- [ ] Configure email settings
- [ ] Set up SSL/HTTPS
- [ ] Configure static file serving
- [ ] Set up monitoring and logging
- [ ] Configure backup procedures

### Environment Variables
See `.env.example` for all available configuration options.

## 📊 System Requirements

### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB
- **Database**: PostgreSQL 12+ or Oracle 19c+
- **Cache**: Redis 6+

### Recommended Requirements
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **Database**: PostgreSQL 14+ or Oracle 21c+
- **Cache**: Redis 7+

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation in `/docs/`
- Review the API documentation at `/api/v1/docs/`

## 🔄 Version History

- **v2.0.0** - Enhanced Enterprise Edition with hierarchical access control
- **v1.0.0** - Initial release with basic evaluation system
