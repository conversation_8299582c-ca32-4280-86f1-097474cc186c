using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.EntityFrameworkCore;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service for managing evaluation operations
    /// </summary>
    public interface IEvaluationService
    {
        Task<List<Evaluation>> GetEvaluationsAsync(string? searchTerm = null, EvaluationStatus? status = null, int? departmentId = null);
        Task<Evaluation?> GetEvaluationByIdAsync(int id);
        Task<Evaluation> CreateEvaluationAsync(Evaluation evaluation);
        Task<Evaluation> UpdateEvaluationAsync(Evaluation evaluation);
        Task<bool> DeleteEvaluationAsync(int id);
        Task<bool> SubmitEvaluationAsync(int id);
        Task<bool> ApproveEvaluationAsync(int id, string approverId);
        Task<bool> RejectEvaluationAsync(int id, string rejectorId, string reason);
        Task<List<EvaluationCategory>> GetEvaluationCategoriesAsync();
        Task<List<EvaluationQuestion>> GetEvaluationQuestionsAsync(int? categoryId = null);
        Task<decimal> CalculateEvaluationScoreAsync(int evaluationId);
        Task<List<ApplicationUser>> GetEvaluableEmployeesAsync(int? departmentId = null);
        Task<bool> HasExistingEvaluationAsync(string employeeId, DateTime periodStart, DateTime periodEnd);
        Task<List<Evaluation>> GetEvaluationsByEmployeeAsync(string employeeId);
        Task<List<Evaluation>> GetEvaluationsByEvaluatorAsync(string evaluatorId);
        Task<Dictionary<string, object>> GetEvaluationStatisticsAsync();
    }

    public class EvaluationService : IEvaluationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<EvaluationService> _logger;

        public EvaluationService(ApplicationDbContext context, ILogger<EvaluationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<List<Evaluation>> GetEvaluationsAsync(string? searchTerm = null, EvaluationStatus? status = null, int? departmentId = null)
        {
            var query = _context.Evaluations
                .Include(e => e.Employee)
                .ThenInclude(e => e.PrimaryDepartment)
                .Include(e => e.Evaluator)
                .Include(e => e.Responses)
                .ThenInclude(r => r.Question)
                .Where(e => !e.IsDeleted);

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(e => 
                    e.Employee.EnglishName.Contains(searchTerm) ||
                    e.Employee.ArabicName.Contains(searchTerm) ||
                    e.Employee.EmployeeId.Contains(searchTerm));
            }

            if (status.HasValue)
            {
                query = query.Where(e => e.Status == status.Value);
            }

            if (departmentId.HasValue)
            {
                query = query.Where(e => e.Employee.PrimaryDepartmentId == departmentId.Value);
            }

            return await query.OrderByDescending(e => e.CreatedAt).ToListAsync();
        }

        public async Task<Evaluation?> GetEvaluationByIdAsync(int id)
        {
            return await _context.Evaluations
                .Include(e => e.Employee)
                .ThenInclude(e => e.PrimaryDepartment)
                .Include(e => e.Evaluator)
                .Include(e => e.Responses)
                .ThenInclude(r => r.Question)
                .ThenInclude(q => q.Category)
                .FirstOrDefaultAsync(e => e.Id == id && !e.IsDeleted);
        }

        public async Task<Evaluation> CreateEvaluationAsync(Evaluation evaluation)
        {
            evaluation.CreatedAt = DateTime.UtcNow;
            evaluation.UpdatedAt = DateTime.UtcNow;
            evaluation.Status = EvaluationStatus.DRAFT;

            _context.Evaluations.Add(evaluation);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Created evaluation {evaluation.Id} for employee {evaluation.EmployeeId}");
            return evaluation;
        }

        public async Task<Evaluation> UpdateEvaluationAsync(Evaluation evaluation)
        {
            evaluation.UpdatedAt = DateTime.UtcNow;
            
            _context.Evaluations.Update(evaluation);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Updated evaluation {evaluation.Id}");
            return evaluation;
        }

        public async Task<bool> DeleteEvaluationAsync(int id)
        {
            var evaluation = await _context.Evaluations.FindAsync(id);
            if (evaluation == null)
                return false;

            evaluation.IsDeleted = true;
            evaluation.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            _logger.LogInformation($"Deleted evaluation {id}");
            return true;
        }

        public async Task<bool> SubmitEvaluationAsync(int id)
        {
            var evaluation = await GetEvaluationByIdAsync(id);
            if (evaluation == null || evaluation.Status != EvaluationStatus.DRAFT)
                return false;

            evaluation.Submit();
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Submitted evaluation {id}");
            return true;
        }

        public async Task<bool> ApproveEvaluationAsync(int id, string approverId)
        {
            var evaluation = await GetEvaluationByIdAsync(id);
            var approver = await _context.Users.FindAsync(approverId);
            
            if (evaluation == null || approver == null || evaluation.Status != EvaluationStatus.SUBMITTED)
                return false;

            evaluation.Approve(approver);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Approved evaluation {id} by {approverId}");
            return true;
        }

        public async Task<bool> RejectEvaluationAsync(int id, string rejectorId, string reason)
        {
            var evaluation = await GetEvaluationByIdAsync(id);
            var rejector = await _context.Users.FindAsync(rejectorId);
            
            if (evaluation == null || rejector == null || evaluation.Status != EvaluationStatus.SUBMITTED)
                return false;

            evaluation.Reject(rejector, reason);
            await _context.SaveChangesAsync();

            _logger.LogInformation($"Rejected evaluation {id} by {rejectorId}");
            return true;
        }

        public async Task<List<EvaluationCategory>> GetEvaluationCategoriesAsync()
        {
            return await _context.EvaluationCategories
                .Include(c => c.Questions.Where(q => q.IsActive && !q.IsDeleted))
                .Where(c => c.IsActive && !c.IsDeleted)
                .OrderBy(c => c.Order)
                .ToListAsync();
        }

        public async Task<List<EvaluationQuestion>> GetEvaluationQuestionsAsync(int? categoryId = null)
        {
            var query = _context.EvaluationQuestions
                .Include(q => q.Category)
                .Where(q => q.IsActive && !q.IsDeleted);

            if (categoryId.HasValue)
            {
                query = query.Where(q => q.CategoryId == categoryId.Value);
            }

            return await query.OrderBy(q => q.Category.Order).ThenBy(q => q.Order).ToListAsync();
        }

        public async Task<decimal> CalculateEvaluationScoreAsync(int evaluationId)
        {
            var evaluation = await GetEvaluationByIdAsync(evaluationId);
            if (evaluation == null)
                return 0;

            evaluation.CalculateScore();
            return evaluation.TotalScore ?? 0;
        }

        public async Task<List<ApplicationUser>> GetEvaluableEmployeesAsync(int? departmentId = null)
        {
            var query = _context.Users
                .Include(u => u.PrimaryDepartment)
                .Where(u => u.IsActive && !u.IsDeleted && u.Role == UserRole.EMPLOYEE);

            if (departmentId.HasValue)
            {
                query = query.Where(u => u.PrimaryDepartmentId == departmentId.Value);
            }

            return await query.OrderBy(u => u.EnglishName).ToListAsync();
        }

        public async Task<bool> HasExistingEvaluationAsync(string employeeId, DateTime periodStart, DateTime periodEnd)
        {
            return await _context.Evaluations
                .AnyAsync(e => e.EmployeeId == employeeId && 
                              !e.IsDeleted &&
                              e.PeriodStart <= periodEnd && 
                              e.PeriodEnd >= periodStart);
        }

        public async Task<List<Evaluation>> GetEvaluationsByEmployeeAsync(string employeeId)
        {
            return await _context.Evaluations
                .Include(e => e.Evaluator)
                .Include(e => e.Responses)
                .Where(e => e.EmployeeId == employeeId && !e.IsDeleted)
                .OrderByDescending(e => e.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<Evaluation>> GetEvaluationsByEvaluatorAsync(string evaluatorId)
        {
            return await _context.Evaluations
                .Include(e => e.Employee)
                .ThenInclude(e => e.PrimaryDepartment)
                .Include(e => e.Responses)
                .Where(e => e.EvaluatorId == evaluatorId && !e.IsDeleted)
                .OrderByDescending(e => e.CreatedAt)
                .ToListAsync();
        }

        public async Task<Dictionary<string, object>> GetEvaluationStatisticsAsync()
        {
            var totalEvaluations = await _context.Evaluations.CountAsync(e => !e.IsDeleted);
            var pendingEvaluations = await _context.Evaluations.CountAsync(e => !e.IsDeleted && e.Status == EvaluationStatus.SUBMITTED);
            var approvedEvaluations = await _context.Evaluations.CountAsync(e => !e.IsDeleted && e.Status == EvaluationStatus.APPROVED);
            var draftEvaluations = await _context.Evaluations.CountAsync(e => !e.IsDeleted && e.Status == EvaluationStatus.DRAFT);
            
            var averageScore = await _context.Evaluations
                .Where(e => !e.IsDeleted && e.Status == EvaluationStatus.APPROVED)
                .AverageAsync(e => (double?)e.TotalScore) ?? 0;

            var departmentStats = await _context.Departments
                .Where(d => d.IsActive && !d.IsDeleted)
                .Select(d => new
                {
                    DepartmentName = d.NameEn,
                    DepartmentNameAr = d.NameAr,
                    EmployeeCount = d.PrimaryUsers.Count(u => u.IsActive && !u.IsDeleted),
                    EvaluationCount = d.PrimaryUsers
                        .SelectMany(u => u.EmployeeEvaluations)
                        .Count(e => !e.IsDeleted),
                    AverageScore = d.PrimaryUsers
                        .SelectMany(u => u.EmployeeEvaluations)
                        .Where(e => !e.IsDeleted && e.Status == EvaluationStatus.APPROVED)
                        .Average(e => (double?)e.TotalScore) ?? 0
                })
                .ToListAsync();

            return new Dictionary<string, object>
            {
                ["TotalEvaluations"] = totalEvaluations,
                ["PendingEvaluations"] = pendingEvaluations,
                ["ApprovedEvaluations"] = approvedEvaluations,
                ["DraftEvaluations"] = draftEvaluations,
                ["AverageScore"] = (decimal)averageScore,
                ["DepartmentStatistics"] = departmentStats
            };
        }
    }
}
