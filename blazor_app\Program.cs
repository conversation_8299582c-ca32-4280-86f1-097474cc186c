using EmployeeRatingSystem.Blazor.Components;
using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using EmployeeRatingSystem.Blazor.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Localization;
using Microsoft.EntityFrameworkCore;
using System.Globalization;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// Configure database
builder.Services.ConfigureDatabase(builder.Configuration);

// Configure Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    // Password settings
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = true;
    options.Password.RequiredLength = 6;
    options.Password.RequiredUniqueChars = 1;

    // Lockout settings
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;

    // User settings
    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
    options.User.RequireUniqueEmail = true;

    // Sign in settings
    options.SignIn.RequireConfirmedEmail = false;
    options.SignIn.RequireConfirmedPhoneNumber = false;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// Configure localization
builder.Services.AddLocalization(options => options.ResourcesPath = "Resources");
builder.Services.Configure<RequestLocalizationOptions>(options =>
{
    var supportedCultures = new[]
    {
        new CultureInfo("en"),
        new CultureInfo("ar")
    };

    options.DefaultRequestCulture = new RequestCulture("en");
    options.SupportedCultures = supportedCultures;
    options.SupportedUICultures = supportedCultures;

    options.RequestCultureProviders.Insert(0, new QueryStringRequestCultureProvider());
    options.RequestCultureProviders.Insert(1, new CookieRequestCultureProvider());
});

// Add custom services
builder.Services.AddHttpContextAccessor();
builder.Services.AddScoped<ILocalizationService, LocalizationService>();
builder.Services.AddScoped<IBreadcrumbService, BreadcrumbService>();
builder.Services.AddScoped<IDataSeedingService, DataSeedingService>();
builder.Services.AddScoped<IEvaluationService, EvaluationService>();
builder.Services.AddScoped<IEmployeeManagementService, EmployeeManagementService>();
builder.Services.AddScoped<IEmployeeAuthenticationService, EmployeeAuthenticationService>();
builder.Services.AddScoped<IEvaluationCalculationService, EvaluationCalculationService>();
builder.Services.AddScoped<IEvaluationDataSeedingService, EvaluationDataSeedingService>();
builder.Services.AddScoped<DashboardStatisticsService>();

// Add authorization
builder.Services.AddAuthorization();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

// Configure localization
var supportedCultures = new[] { "en", "ar" };
var localizationOptions = new RequestLocalizationOptions()
    .SetDefaultCulture(supportedCultures[0])
    .AddSupportedCultures(supportedCultures)
    .AddSupportedUICultures(supportedCultures);

app.UseRequestLocalization(localizationOptions);

app.UseStaticFiles();
app.UseAntiforgery();

// Add authentication and authorization
app.UseAuthentication();
app.UseAuthorization();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

// Add authentication endpoints
app.MapPost("/api/auth/login", async (HttpContext context, IEmployeeAuthenticationService authService) =>
{
    var form = await context.Request.ReadFormAsync();
    var employeeId = form["employeeId"].ToString();
    var password = form["password"].ToString();
    var rememberMe = form["rememberMe"].ToString() == "true";
    var returnUrl = form["returnUrl"].ToString();

    if (string.IsNullOrEmpty(employeeId) || string.IsNullOrEmpty(password))
    {
        return Results.Redirect("/login?error=validation");
    }

    try
    {
        var result = await authService.PerformActualSignInAsync(employeeId, password, rememberMe);

        if (result.Succeeded)
        {
            var redirectUrl = string.IsNullOrEmpty(returnUrl) ? "/dashboard" : returnUrl;
            return Results.Redirect(redirectUrl);
        }
        else if (result.IsLockedOut)
        {
            return Results.Redirect("/login?error=lockedout");
        }
        else
        {
            return Results.Redirect("/login?error=invalid");
        }
    }
    catch (Exception)
    {
        return Results.Redirect("/login?error=system");
    }
});

app.MapPost("/api/auth/logout", async (HttpContext context, IEmployeeAuthenticationService authService) =>
{
    await authService.SignOutAsync();
    return Results.Redirect("/login");
});

// Add evaluation data seeding endpoint (for testing)
app.MapPost("/api/evaluation/seed-sample-data", async (IEvaluationDataSeedingService seedingService) =>
{
    try
    {
        await seedingService.SeedCurrentMonthDataAsync();
        return Results.Ok(new { message = "Sample data seeded successfully" });
    }
    catch (Exception ex)
    {
        return Results.BadRequest(new { error = ex.Message });
    }
});

// Initialize database with proper error handling
try
{
    using var scope = app.Services.CreateScope();
    var services = scope.ServiceProvider;
    var logger = services.GetRequiredService<ILogger<Program>>();

    logger.LogInformation("Starting database initialization...");

    var context = services.GetRequiredService<ApplicationDbContext>();
    var userManager = services.GetRequiredService<UserManager<ApplicationUser>>();
    var roleManager = services.GetRequiredService<RoleManager<IdentityRole>>();

    // Apply migrations if database provider supports it
    if (DatabaseConfiguration.SupportsMigrations(builder.Configuration))
    {
        context.Database.Migrate();
        logger.LogInformation("Database migrations applied successfully.");
    }

    // Seed initial data
    var seedingService = services.GetRequiredService<IDataSeedingService>();
    await seedingService.SeedAsync();
    logger.LogInformation("Database seeding completed successfully.");
}
catch (Exception ex)
{
    var logger = app.Services.GetRequiredService<ILogger<Program>>();
    logger.LogError(ex, "Database initialization failed. Application will continue without database.");
}

app.Run();
