using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Individual responses to evaluation questions.
    /// Equivalent to Django's EvaluationResponse model.
    /// </summary>
    public class EvaluationResponse : BaseModel
    {
        /// <summary>
        /// Evaluation ID
        /// </summary>
        [Required]
        public int EvaluationId { get; set; }

        /// <summary>
        /// Evaluation navigation property
        /// </summary>
        public virtual Evaluation Evaluation { get; set; } = null!;

        /// <summary>
        /// Question ID
        /// </summary>
        [Required]
        public int QuestionId { get; set; }

        /// <summary>
        /// Question navigation property
        /// </summary>
        public virtual EvaluationQuestion Question { get; set; } = null!;

        /// <summary>
        /// Numeric score for this question (0-10 with decimal support)
        /// </summary>
        [Required]
        [Range(0, 10)]
        [Column(TypeName = "decimal(4,1)")]
        [Display(Name = "Score")]
        public decimal Score { get; set; }

        /// <summary>
        /// Additional comments in English
        /// </summary>
        [Display(Name = "Comments (English)")]
        public string CommentsEn { get; set; } = string.Empty;

        /// <summary>
        /// Additional comments in Arabic
        /// </summary>
        [Display(Name = "Comments (Arabic)")]
        public string CommentsAr { get; set; } = string.Empty;

        /// <summary>
        /// Get comments based on language preference
        /// </summary>
        public string GetComments(string language = "en")
        {
            return language == "ar" ? CommentsAr : CommentsEn;
        }

        /// <summary>
        /// Validate that the score is within the allowed range for the question
        /// </summary>
        public bool IsScoreValid()
        {
            return Score >= Question.MinScore && Score <= Question.MaxScore;
        }

        /// <summary>
        /// Calculate the weighted score for this response
        /// </summary>
        public decimal CalculateWeightedScore()
        {
            return Question.CalculateWeightedScore(Score);
        }

        /// <summary>
        /// Get the percentage score (0-100) for this response
        /// </summary>
        public decimal GetPercentageScore()
        {
            return (Score / Question.MaxScore) * 100;
        }

        /// <summary>
        /// Get the color code for this score based on percentage
        /// </summary>
        public string GetScoreColorCode()
        {
            decimal percentage = GetPercentageScore();
            
            if (percentage >= 90)
                return "#10b981"; // Green
            else if (percentage >= 80)
                return "#3b82f6"; // Blue
            else if (percentage >= 70)
                return "#f59e0b"; // Yellow
            else
                return "#ef4444"; // Red
        }

        public override string ToString()
        {
            return $"Response to '{Question?.TextEn}' - Score: {Score}";
        }
    }
}
