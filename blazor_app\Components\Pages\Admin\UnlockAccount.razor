@page "/admin/unlock"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Services
@inject IEmployeeAuthenticationService EmployeeAuthService

<h3>Unlock User Account</h3>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Unlock User Account</h5>
                    
                    @if (!string.IsNullOrEmpty(message))
                    {
                        <div class="alert @(isSuccess ? "alert-success" : "alert-danger")">
                            @message
                        </div>
                    }
                    
                    <div class="mb-3">
                        <label for="employeeId" class="form-label">Employee ID:</label>
                        <input @bind="employeeId" class="form-control" id="employeeId" placeholder="Enter Employee ID to unlock" />
                    </div>
                    
                    <button @onclick="UnlockUserAccount" class="btn btn-primary" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm me-2"></span>
                        }
                        Unlock User
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string employeeId = "";
    private string message = "";
    private bool isSuccess = false;
    private bool isLoading = false;

    private async Task UnlockUserAccount()
    {
        if (string.IsNullOrWhiteSpace(employeeId))
        {
            message = "Please enter an Employee ID";
            isSuccess = false;
            return;
        }

        isLoading = true;
        message = "";
        StateHasChanged();

        try
        {
            var result = await EmployeeAuthService.UnlockUserAsync(employeeId);
            if (result)
            {
                message = $"User {employeeId} has been unlocked successfully!";
                isSuccess = true;
                employeeId = "";
            }
            else
            {
                message = $"Failed to unlock user {employeeId}. User may not exist.";
                isSuccess = false;
            }
        }
        catch (Exception ex)
        {
            message = $"Error unlocking user: {ex.Message}";
            isSuccess = false;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
