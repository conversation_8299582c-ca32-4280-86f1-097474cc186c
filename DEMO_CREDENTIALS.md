# Employee Rating System - Demo Credentials

## 🚀 Quick Start

Access the system at: **http://localhost:8000/users/login/**

## 👥 Demo User Accounts

### 🔑 Login Credentials

| Role | Username | Password | Access Level | Description |
|------|----------|----------|--------------|-------------|
| **Super Admin** | `superadmin` | `admin123` | Full System | Complete system administration and configuration |
| **IT Manager** | `it_manager` | `manager123` | IT Division | IT Division management and oversight |
| **HR Manager** | `hr_manager` | `manager123` | HR Division | HR Division management and oversight |
| **Dev Supervisor** | `dev_supervisor` | `supervisor123` | Development Team | Software development team supervision |
| **QA Supervisor** | `qa_supervisor` | `supervisor123` | QA Team | Quality assurance team supervision |
| **Quality Lead** | `quality_lead` | `quality123` | Quality Monitoring | Cross-departmental quality monitoring (read-only) |
| **Senior Developer** | `dev_senior` | `employee123` | Employee View | Personal performance and development tracking |
| **Junior Developer** | `dev_junior` | `employee123` | Employee View | Personal performance and development tracking |
| **QA Tester** | `qa_tester` | `employee123` | Employee View | Personal performance and development tracking |

## 🏢 Organizational Structure

### Company Hierarchy
```
TechCorp International (شركة تك كورب الدولية)
├── Information Technology Division (قسم تقنية المعلومات)
│   ├── Software Development (تطوير البرمجيات)
│   └── Quality Assurance (ضمان الجودة)
├── Human Resources Division (قسم الموارد البشرية)
│   └── Recruitment & Talent Acquisition (التوظيف واكتساب المواهب)
└── Sales & Marketing Division (قسم المبيعات والتسويق)
    └── Regional Sales (المبيعات الإقليمية)
```

### User-Department Assignments
- **IT Manager**: Manages entire IT Division
- **HR Manager**: Manages entire HR Division
- **Dev Supervisor**: Supervises Software Development department
- **QA Supervisor**: Supervises Quality Assurance department
- **Employees**: Assigned to respective departments

## 📊 Evaluation Categories (Based on PRD)

### 1. Attendance & Punctuality (الحضور والانصراف) - 25%
- Number of attendance days (40%)
- Total hours worked (30%)
- Punctuality rate (20%)
- Unplanned absence frequency (10%)

### 2. Work Volume (حجم العمل) - 35%
- Work quality volume (50%)
- Monthly work volume (30%)
- Task completion rate (20%)

### 3. Creative Work (العمل الإبداعي) - 20%
- Innovation and new ideas (30%)
- Process improvements (25%)
- Problem-solving approach (25%)
- Initiative taken (20%)

### 4. Direct Supervisor Evaluation (تقييم المسؤول المباشر) - 20%
- Communication style (50%)
- Collaboration (30%)
- Professional behavior (20%)

## 🎯 Testing Scenarios

### Scenario 1: Super Admin Workflow
1. Login as `superadmin` / `admin123`
2. Access complete system overview
3. Manage users and departments
4. Configure evaluation criteria
5. View system-wide analytics

### Scenario 2: Manager Workflow
1. Login as `it_manager` / `manager123`
2. View IT division hierarchy
3. Manage supervisors and employees
4. Create and review evaluations
5. Generate department reports

### Scenario 3: Supervisor Workflow
1. Login as `dev_supervisor` / `supervisor123`
2. View direct report team
3. Conduct employee evaluations
4. Track team performance
5. Submit evaluations for approval

### Scenario 4: Quality Team Workflow
1. Login as `quality_lead` / `quality123`
2. Monitor evaluation quality across departments
3. View compliance reports
4. Access audit trails
5. Generate quality metrics

### Scenario 5: Employee Workflow
1. Login as `dev_senior` / `employee123`
2. View personal performance scorecard
3. Track evaluation history
4. Access development recommendations
5. View position in organizational hierarchy

## 🌐 Language Testing

### Arabic Interface Testing
1. Click the **ع** button in navigation to switch to Arabic
2. Verify RTL layout and Arabic text display
3. Test all functionality in Arabic interface
4. Verify user language preference is saved

### English Interface Testing
1. Click the **E** button in navigation to switch to English
2. Verify LTR layout and English text display
3. Test all functionality in English interface
4. Verify user language preference is saved

## 🔐 Role-Based Access Control Testing

### Access Matrix Verification
| Feature | Super Admin | Manager | Supervisor | Quality Team | Employee |
|---------|-------------|---------|------------|--------------|----------|
| User Management | ✅ | ❌ | ❌ | ❌ | ❌ |
| Department Management | ✅ | Limited | ❌ | ❌ | ❌ |
| Evaluation Configuration | ✅ | ❌ | ❌ | ❌ | ❌ |
| Cross-Department Access | ✅ | ❌ | ❌ | Read-only | ❌ |
| Evaluation Creation | ✅ | ✅ | ✅ | ❌ | ❌ |
| Personal Data Access | ✅ | Hierarchy | Direct Reports | Read-only | Own Only |

## 📝 Demo Data Features

### ✅ Implemented
- Complete organizational hierarchy
- Role-based user accounts
- Bilingual evaluation categories and questions
- Language switching with user preferences
- Professional UI with Arabic/English support

### 🚧 In Progress
- Department-user relationship assignments
- Sample evaluation data
- Evaluation workflow implementation
- Advanced reporting and analytics

## 🎯 Implemented Features

### ✅ Complete System Features
- **Bilingual Support**: Full Arabic/English interface with RTL support
- **Role-Based Access Control**: 5 user roles with proper permissions
- **Organizational Hierarchy**: Multi-level department structure
- **Evaluation System**: Complete evaluation framework with PRD-based categories
- **User Management**: Registration, approval, and role assignment
- **Language Preferences**: User-specific language settings with E/ع switcher
- **Professional UI**: Modern, responsive design with Bootstrap 5

### ✅ Evaluation System (Based on PRD)
- **4 Main Categories**: Attendance (25%), Work Volume (35%), Creative Work (20%), Supervisor Evaluation (20%)
- **15 Detailed Questions**: Weighted sub-criteria for comprehensive evaluation
- **Bilingual Questions**: All evaluation criteria in Arabic and English
- **Dynamic Scoring**: Configurable weights and automatic calculation
- **Workflow Management**: Draft → Submitted → Approved/Rejected status flow

### ✅ User Roles & Permissions
- **Super Admin**: Full system access, user/department management, evaluation configuration
- **Manager**: Department hierarchy management, supervisor evaluation, team oversight
- **Supervisor**: Direct report evaluation, team performance tracking
- **Quality Team**: Cross-departmental quality monitoring (read-only)
- **Employee**: Personal performance view, evaluation history access

### ✅ Demo Data
- **9 Sample Users**: Representing all roles with realistic names and assignments
- **Organizational Structure**: TechCorp International with IT, HR, and Sales divisions
- **Department Hierarchy**: Multi-level structure with proper relationships
- **Evaluation Categories**: All PRD-specified categories and questions populated

## 🛠️ Development Commands

### Reset Demo Data
```bash
python manage.py setup_demo --reset
```

### Create Demo Data (without reset)
```bash
python manage.py setup_demo
```

### Run Development Server
```bash
python manage.py runserver
```

### Apply Migrations
```bash
python manage.py migrate
```

## 📞 Support

For any issues or questions about the demo system:
1. Check the console output for detailed error messages
2. Verify all migrations are applied: `python manage.py migrate`
3. Ensure demo data is created: `python manage.py setup_demo --reset`
4. Access the system at: http://localhost:8000/

---

**Last Updated**: July 13, 2025  
**System Version**: Demo v1.0  
**Languages**: Arabic (العربية) / English
