@page "/login"
@layout AuthLayout
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.Components.Authorization
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Components.Layout
@using System.ComponentModel.DataAnnotations

@inherits LocalizedComponentBase
@inject IEmployeeAuthenticationService EmployeeAuthService
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>@GetPageTitle("Login", "تسجيل الدخول")</PageTitle>

<div class="login-page">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left Side - Enhanced Branding -->
            <div class="col-lg-7 d-none d-lg-flex align-items-center justify-content-center login-branding-section">
                <div class="text-center text-white w-100">
                    <!-- Main Logo and Title -->
                    <div class="login-brand-header mb-5">
                        <div class="login-logo-container mb-4">
                            <div class="login-logo-circle">
                                <i class="fas fa-chart-line fa-3x"></i>
                            </div>
                        </div>
                        <h1 class="login-brand-title mb-3">
                            @L("Employee Rating System", "نظام تقييم الموظفين")
                        </h1>
                        <p class="login-brand-subtitle">
                            @L("Comprehensive performance evaluation platform", "منصة شاملة لتقييم الأداء")
                        </p>
                    </div>

                    <!-- Feature Cards -->
                    <div class="login-features-grid">
                        <div class="login-feature-card">
                            <div class="login-feature-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <h6 class="login-feature-title">@L("User Management", "إدارة المستخدمين")</h6>
                            <p class="login-feature-desc">@L("Manage employees and roles", "إدارة الموظفين والأدوار")</p>
                        </div>
                        <div class="login-feature-card">
                            <div class="login-feature-icon">
                                <i class="fas fa-building"></i>
                            </div>
                            <h6 class="login-feature-title">@L("Departments", "الأقسام")</h6>
                            <p class="login-feature-desc">@L("Organize by departments", "تنظيم حسب الأقسام")</p>
                        </div>
                        <div class="login-feature-card">
                            <div class="login-feature-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <h6 class="login-feature-title">@L("Evaluations", "التقييمات")</h6>
                            <p class="login-feature-desc">@L("Performance tracking", "تتبع الأداء")</p>
                        </div>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="login-decorative-elements">
                        <div class="login-floating-shape login-shape-1"></div>
                        <div class="login-floating-shape login-shape-2"></div>
                        <div class="login-floating-shape login-shape-3"></div>
                        <div class="login-floating-shape login-shape-4"></div>
                        <div class="login-floating-shape login-shape-5"></div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Enhanced Login Form -->
            <div class="col-lg-5 d-flex align-items-center justify-content-center login-form-section">
                <div class="login-form-container w-100">
                    <!-- Mobile Logo -->
                    <div class="d-lg-none text-center mb-4">
                        <div class="login-mobile-logo">
                            <i class="fas fa-chart-line fa-3x text-primary"></i>
                        </div>
                    </div>

                    <!-- Form Header -->
                    <div class="login-form-header text-center mb-4">
                        <h2 class="login-form-title">@L("Welcome Back", "مرحباً بعودتك")</h2>
                        <p class="login-form-subtitle">@L("Sign in to your account", "سجل دخولك إلى حسابك")</p>
                    </div>

                    <!-- Error Message -->
                    @if (!string.IsNullOrEmpty(errorMessage))
                    {
                        <div class="login-error-alert" role="alert">
                            <div class="login-error-content">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>@errorMessage</span>
                            </div>
                        </div>
                    }

                    <!-- Login Form -->
                    <div class="login-form">

                        <!-- Employee ID Field -->
                        <div class="login-form-group">
                            <label for="employeeId" class="login-form-label">
                                <i class="fas fa-id-card"></i>
                                @L("Employee ID", "رقم الموظف")
                            </label>
                            <div class="login-input-wrapper">
                                <input type="text" @bind="loginModel.EmployeeId" class="login-form-input"
                                       id="employeeId" placeholder="@L("Enter your Employee ID", "أدخل رقم الموظف")"
                                       autocomplete="username" required />
                                <div class="login-input-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Password Field -->
                        <div class="login-form-group">
                            <label for="password" class="login-form-label">
                                <i class="fas fa-lock"></i>
                                @L("Password", "كلمة المرور")
                            </label>
                            <div class="login-input-wrapper">
                                <input type="@(showPassword ? "text" : "password")" @bind="loginModel.Password"
                                       class="login-form-input" id="password"
                                       placeholder="@L("Enter your password", "أدخل كلمة المرور")"
                                       autocomplete="current-password" required />
                                <div class="login-input-icon">
                                    <i class="fas fa-lock"></i>
                                </div>
                                <button type="button" class="login-password-toggle" @onclick="TogglePasswordVisibility">
                                    <i class="fas fa-@(showPassword ? "eye-slash" : "eye")"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Remember Me -->
                        <div class="login-form-group">
                            <div class="login-checkbox-wrapper">
                                <input type="checkbox" @bind="loginModel.RememberMe" class="login-checkbox" id="rememberMe" />
                                <label class="login-checkbox-label" for="rememberMe">
                                    <span class="login-checkbox-custom"></span>
                                    @L("Remember me", "تذكرني")
                                </label>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="login-form-group">
                            <button type="button" class="login-submit-btn" disabled="@isLoading" @onclick="HandleButtonClick">
                                <span class="login-submit-content" style="@(isLoading ? "opacity: 0;" : "")">
                                    <i class="fas fa-sign-in-alt"></i>
                                    @L("Sign In", "تسجيل الدخول")
                                </span>
                                <div class="login-submit-loader" style="@(isLoading ? "opacity: 1;" : "")">
                                    <div class="login-spinner"></div>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Form Footer -->
                    <div class="login-form-footer">
                        <!-- Forgot Password Link -->
                        <div class="login-forgot-password">
                            <a href="/forgot-password" class="login-link">
                                @L("Forgot your password?", "نسيت كلمة المرور؟")
                            </a>
                        </div>

                        <!-- Language Switcher -->
                        <div class="login-language-section">
                            <div class="login-language-label">
                                @L("Language", "اللغة")
                            </div>
                            <div class="login-language-switcher">
                                <LanguageSwitcher ShowDropdown="false" Size="sm" />
                            </div>
                        </div>
                    </div>

                    <!-- Demo Credentials Section -->
                    @if (showDemoInfo)
                    {
                        <div class="login-demo-credentials">
                            <div class="login-demo-header">
                                <div class="login-demo-title">
                                    <i class="fas fa-info-circle"></i>
                                    @L("Demo Credentials", "بيانات تجريبية")
                                </div>
                                <button type="button" class="login-demo-close" @onclick="() => showDemoInfo = false">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="login-demo-content">
                                <div class="login-demo-item">
                                    <span class="login-demo-role">@L("Super Admin", "مدير النظام"):</span>
                                    <span class="login-demo-value">EMP001</span>
                                </div>
                                <div class="login-demo-item">
                                    <span class="login-demo-role">@L("Manager", "المدير"):</span>
                                    <span class="login-demo-value">EMP002</span>
                                </div>
                                <div class="login-demo-item">
                                    <span class="login-demo-role">@L("Password", "كلمة المرور"):</span>
                                    <span class="login-demo-value">Password123!</span>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="login-demo-toggle">
                            <button type="button" class="login-demo-btn" @onclick="() => showDemoInfo = true">
                                <i class="fas fa-key"></i>
                                @L("Show demo credentials", "إظهار البيانات التجريبية")
                            </button>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string errorMessage = string.Empty;
    private string? returnUrl;
    private bool showPassword = false;
    private bool showDemoInfo = false;
    private bool isLoading = false;

    private LoginModel loginModel { get; set; } = new();

    public class LoginModel
    {
        [Required(ErrorMessage = "Employee ID is required")]
        public string EmployeeId { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; } = false;
    }

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            Navigation.NavigateTo("/dashboard");
        }

        // Get return URL and error from query parameters
        var uri = new Uri(Navigation.Uri);
        var query = Microsoft.AspNetCore.WebUtilities.QueryHelpers.ParseQuery(uri.Query);

        if (query.TryGetValue("returnUrl", out var returnUrlValue))
        {
            returnUrl = returnUrlValue.FirstOrDefault();
        }

        if (query.TryGetValue("error", out var errorValue))
        {
            var error = errorValue.FirstOrDefault();
            errorMessage = error switch
            {
                "validation" => L("Please fill in all required fields.", "يرجى ملء جميع الحقول المطلوبة."),
                "invalid" => L("Invalid Employee ID or password.", "رقم الموظف أو كلمة المرور غير صحيحة."),
                "lockedout" => L("Account is locked. Please contact administrator.", "الحساب مقفل. يرجى الاتصال بالمدير."),
                "system" => L("System error occurred. Please try again.", "حدث خطأ في النظام. يرجى المحاولة مرة أخرى."),
                _ => string.Empty
            };
        }
    }

    protected override void OnParametersSet()
    {
        // Initialize login model with demo data if it's empty
        if (string.IsNullOrEmpty(loginModel.EmployeeId))
        {
            loginModel.EmployeeId = "EMP001";
        }
        if (string.IsNullOrEmpty(loginModel.Password))
        {
            loginModel.Password = "Password123!";
        }
    }



    private void TogglePasswordVisibility()
    {
        showPassword = !showPassword;
        StateHasChanged();
    }

    private async Task HandleButtonClick()
    {
        Console.WriteLine("HandleButtonClick called!");
        await HandleLogin();
    }

    private async Task HandleLogin()
    {
        if (isLoading) return;

        isLoading = true;
        errorMessage = string.Empty;
        StateHasChanged();

        try
        {
            // Trim whitespace from inputs
            var employeeId = loginModel.EmployeeId?.Trim() ?? string.Empty;
            var password = loginModel.Password?.Trim() ?? string.Empty;

            // Validate inputs
            if (string.IsNullOrEmpty(employeeId) || string.IsNullOrEmpty(password))
            {
                errorMessage = L("Please enter both Employee ID and password.", "يرجى إدخال رقم الموظف وكلمة المرور.");
                return;
            }

            // Enhanced Debug Logging
            Console.WriteLine("=== LOGIN ATTEMPT DEBUG ===");
            Console.WriteLine($"Employee ID: '{employeeId}' (Length: {employeeId.Length})");
            Console.WriteLine($"Password: '{password}' (Length: {password.Length})");
            Console.WriteLine($"Remember Me: {loginModel.RememberMe}");

            // Check if user exists first
            var userExists = await EmployeeAuthService.FindByEmployeeIdAsync(employeeId);
            Console.WriteLine($"User exists in database: {userExists != null}");
            if (userExists != null)
            {
                Console.WriteLine($"User details - ID: {userExists.EmployeeId}, Active: {userExists.IsActive}, Deleted: {userExists.IsDeleted}");
            }

            // Use the correct method for Blazor Server authentication
            var result = await EmployeeAuthService.SignInWithEmployeeIdAsync(
                employeeId,
                password,
                loginModel.RememberMe);

            Console.WriteLine($"Authentication result: Succeeded={result.Succeeded}, IsLockedOut={result.IsLockedOut}, IsNotAllowed={result.IsNotAllowed}");

            if (result.Succeeded)
            {
                Console.WriteLine("Login successful! Refreshing authentication state...");

                // Notify the authentication state provider that the user has signed in
                await Task.Delay(100); // Small delay to ensure sign-in is processed

                // Force refresh of authentication state
                var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
                Console.WriteLine($"Authentication state after login: {authState.User.Identity?.IsAuthenticated}");

                var redirectUrl = string.IsNullOrEmpty(returnUrl) ? "/dashboard" : returnUrl;
                Console.WriteLine($"Redirecting to: {redirectUrl}");
                Navigation.NavigateTo(redirectUrl, forceLoad: true);
            }
            else if (result.IsLockedOut)
            {
                Console.WriteLine("Login failed: Account is locked out");
                errorMessage = L("Account is locked. Please contact administrator.", "الحساب مقفل. يرجى الاتصال بالمدير.");
            }
            else
            {
                Console.WriteLine("Login failed: Invalid credentials");
                errorMessage = L("Invalid Employee ID or password.", "رقم الموظف أو كلمة المرور غير صحيحة.");
            }
        }
        catch (Exception)
        {
            errorMessage = L("System error occurred. Please try again.", "حدث خطأ في النظام. يرجى المحاولة مرة أخرى.");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}