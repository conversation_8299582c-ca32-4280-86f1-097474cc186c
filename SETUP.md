# Employee Rating System - Setup Guide

This guide provides detailed instructions for setting up the Employee Rating System in different environments.

## 📋 Prerequisites

### System Requirements
- **Python**: 3.8 or higher
- **Node.js**: 16+ (for frontend assets, if applicable)
- **Database**: PostgreSQL 12+, Oracle 19c+, or SQLite (development)
- **Cache**: Redis 6+ (recommended)
- **OS**: Windows 10+, macOS 10.15+, or Linux (Ubuntu 20.04+)

### Required Tools
- Git
- Python pip
- Virtual environment tool (venv, virtualenv, or conda)
- Database client (pgAdmin, Oracle SQL Developer, etc.)

## 🚀 Installation Steps

### 1. Repository Setup

```bash
# Clone the repository
git clone <repository-url>
cd employee_rating

# Verify Python version
python --version  # Should be 3.8+
```

### 2. Virtual Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Verify activation
which python  # Should point to venv/bin/python
```

### 3. Dependencies Installation

```bash
# For development
pip install -r requirements-dev.txt

# For production
pip install -r requirements-prod.txt

# Verify installation
pip list | grep Django  # Should show Django 4.2.7
```

### 4. Environment Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
# Use your preferred text editor
nano .env  # or vim .env or code .env
```

### 5. Database Setup

#### Option A: SQLite (Development)
```bash
# Default configuration in .env
DATABASE_ENGINE=sqlite

# No additional setup required
```

#### Option B: PostgreSQL (Recommended)
```bash
# Install PostgreSQL (if not already installed)
# Ubuntu/Debian:
sudo apt-get install postgresql postgresql-contrib

# macOS (with Homebrew):
brew install postgresql

# Windows: Download from https://www.postgresql.org/download/

# Create database and user
sudo -u postgres psql
CREATE DATABASE employee_rating;
CREATE USER rating_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE employee_rating TO rating_user;
\q

# Update .env file
DATABASE_ENGINE=postgresql
POSTGRES_DB=employee_rating
POSTGRES_USER=rating_user
POSTGRES_PASSWORD=your_password
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
```

#### Option C: Oracle (Enterprise)
```bash
# Install Oracle client libraries
# Download from Oracle website or use instant client

# Update .env file
DATABASE_ENGINE=oracle
ORACLE_DB_NAME=xe
ORACLE_USER=employee_rating
ORACLE_PASSWORD=your_password
ORACLE_HOST=localhost
ORACLE_PORT=1521
ORACLE_SERVICE_NAME=xe
```

### 6. Redis Setup (Optional but Recommended)

```bash
# Install Redis
# Ubuntu/Debian:
sudo apt-get install redis-server

# macOS (with Homebrew):
brew install redis

# Windows: Download from https://redis.io/download

# Start Redis service
# Ubuntu/Debian:
sudo systemctl start redis-server

# macOS:
brew services start redis

# Windows: Run redis-server.exe

# Test Redis connection
redis-cli ping  # Should return PONG
```

### 7. Django Setup

```bash
# Generate secret key (optional - one is provided in .env)
python -c "from django.core.management.utils import get_random_secret_key; print(get_random_secret_key())"

# Create database tables
python manage.py makemigrations
python manage.py migrate

# Create superuser account
python manage.py createsuperuser
# Follow prompts to create admin account

# Collect static files (for production)
python manage.py collectstatic --noinput
```

### 8. Initial Data Setup

```bash
# Load initial evaluation categories (optional)
python manage.py loaddata initial_categories.json

# Create sample departments (optional)
python manage.py shell
>>> from departments.models import Department
>>> root = Department.objects.create(name_en="Company", name_ar="الشركة", code="COMP")
>>> sales = Department.objects.create(name_en="Sales", name_ar="المبيعات", code="SALES", parent=root)
>>> hr = Department.objects.create(name_en="HR", name_ar="الموارد البشرية", code="HR", parent=root)
>>> exit()
```

### 9. Verification

```bash
# Run development server
python manage.py runserver

# Test in browser
# Open http://localhost:8000
# Admin interface: http://localhost:8000/admin/
# API documentation: http://localhost:8000/api/v1/docs/

# Run tests
python manage.py test

# Check system status
python manage.py check
```

## 🔧 Configuration Options

### Environment Variables

#### Required Settings
```env
SECRET_KEY=your-secret-key-here
DEBUG=True  # False for production
ALLOWED_HOSTS=localhost,127.0.0.1
DATABASE_ENGINE=sqlite  # or postgresql, oracle
```

#### Database Settings
```env
# PostgreSQL
POSTGRES_DB=employee_rating
POSTGRES_USER=your_user
POSTGRES_PASSWORD=your_password
POSTGRES_HOST=localhost
POSTGRES_PORT=5432

# Oracle
ORACLE_DB_NAME=xe
ORACLE_USER=employee_rating
ORACLE_PASSWORD=your_password
ORACLE_HOST=localhost
ORACLE_PORT=1521
```

#### Optional Settings
```env
# Internationalization
LANGUAGE_CODE=en  # or ar
TIME_ZONE=Asia/Riyadh

# Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# Redis
REDIS_URL=redis://localhost:6379/0

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/employee_rating.log
```

## 🚀 Production Deployment

### 1. Production Settings

```bash
# Use production settings
export DJANGO_SETTINGS_MODULE=employee_rating.settings.production

# Or set in .env
DJANGO_SETTINGS_MODULE=employee_rating.settings.production
```

### 2. Security Configuration

```env
# Security settings
DEBUG=False
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
```

### 3. Web Server Setup

#### Using Gunicorn
```bash
# Install gunicorn (included in requirements-prod.txt)
pip install gunicorn

# Run with gunicorn
gunicorn employee_rating.wsgi:application --bind 0.0.0.0:8000 --workers 4
```

#### Using uWSGI
```bash
# Install uWSGI
pip install uwsgi

# Create uwsgi.ini configuration file
# Run with uWSGI
uwsgi --ini uwsgi.ini
```

### 4. Reverse Proxy (Nginx)

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /static/ {
        alias /path/to/employee_rating/staticfiles/;
    }
    
    location /media/ {
        alias /path/to/employee_rating/media/;
    }
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 🔍 Troubleshooting

### Common Issues

#### Database Connection Errors
```bash
# Check database service status
sudo systemctl status postgresql  # For PostgreSQL
sudo systemctl status redis-server  # For Redis

# Test database connection
python manage.py dbshell
```

#### Migration Issues
```bash
# Reset migrations (development only)
python manage.py migrate --fake-initial

# Show migration status
python manage.py showmigrations

# Apply specific migration
python manage.py migrate app_name migration_name
```

#### Permission Errors
```bash
# Check file permissions
ls -la

# Fix permissions (Linux/macOS)
chmod +x manage.py
chown -R user:group /path/to/project
```

#### Import Errors
```bash
# Verify virtual environment
which python
pip list

# Reinstall dependencies
pip install -r requirements-dev.txt --force-reinstall
```

### Performance Optimization

#### Database Optimization
```bash
# Create database indexes
python manage.py dbshell
CREATE INDEX idx_user_employee_id ON users_customuser(employee_id);
CREATE INDEX idx_dept_code ON departments_department(code);
```

#### Caching Setup
```python
# In settings
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

## 📞 Support

If you encounter issues during setup:

1. Check the logs: `tail -f logs/employee_rating.log`
2. Verify environment variables: `python manage.py diffsettings`
3. Test database connection: `python manage.py dbshell`
4. Check system requirements: `python manage.py check --deploy`
5. Review Django documentation: https://docs.djangoproject.com/

For additional support, create an issue in the repository with:
- Error messages
- Environment details (OS, Python version, database)
- Steps to reproduce the issue
