using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Identity;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Custom user model extending ASP.NET Core Identity's IdentityUser.
    /// Implements the role-based access control system from PRD.
    /// Equivalent to Django's CustomUser model.
    /// </summary>
    public class ApplicationUser : IdentityUser
    {
        /// <summary>
        /// Unique employee identifier
        /// </summary>
        [Required]
        [StringLength(50)]
        [Display(Name = "Employee ID")]
        public string EmployeeId { get; set; } = string.Empty;

        /// <summary>
        /// Full name in Arabic
        /// </summary>
        [Required]
        [StringLength(255)]
        [Display(Name = "Arabic Name (الاسم بالعربية)")]
        public string ArabicName { get; set; } = string.Empty;

        /// <summary>
        /// Full name in English
        /// </summary>
        [Required]
        [StringLength(255)]
        [Display(Name = "English Name")]
        public string EnglishName { get; set; } = string.Empty;

        /// <summary>
        /// User role in the system
        /// </summary>
        [Required]
        [Display(Name = "Role")]
        public UserRole Role { get; set; } = UserRole.EMPLOYEE;

        /// <summary>
        /// Primary department for this user
        /// </summary>
        public int? PrimaryDepartmentId { get; set; }
        public virtual Department? PrimaryDepartment { get; set; }

        /// <summary>
        /// Departments managed by this user (for Managers)
        /// </summary>
        public virtual ICollection<Department> ManagedDepartments { get; set; } = new List<Department>();

        /// <summary>
        /// All departments this user belongs to
        /// </summary>
        public virtual ICollection<UserDepartment> UserDepartments { get; set; } = new List<UserDepartment>();

        /// <summary>
        /// Evaluations received by this user
        /// </summary>
        public virtual ICollection<Evaluation> Evaluations { get; set; } = new List<Evaluation>();

        /// <summary>
        /// Evaluations conducted by this user
        /// </summary>
        public virtual ICollection<Evaluation> ConductedEvaluations { get; set; } = new List<Evaluation>();

        /// <summary>
        /// Evaluations approved by this user
        /// </summary>
        public virtual ICollection<Evaluation> ApprovedEvaluations { get; set; } = new List<Evaluation>();

        /// <summary>
        /// Alias for Evaluations property (for compatibility)
        /// </summary>
        public virtual ICollection<Evaluation> EmployeeEvaluations => Evaluations;

        /// <summary>
        /// Date when user was created
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Date when user was last updated
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Whether this user is active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Whether this user is soft-deleted
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// Date when user was soft-deleted
        /// </summary>
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// Preferred language (en/ar)
        /// </summary>
        [StringLength(10)]
        public string PreferredLanguage { get; set; } = "en";

        /// <summary>
        /// Get display name based on language preference
        /// </summary>
        public string GetDisplayName(string language = "en")
        {
            return language == "ar" ? ArabicName : EnglishName;
        }

        /// <summary>
        /// Get all departments managed by this user (for Managers)
        /// </summary>
        public ICollection<Department> GetManagedDepartments()
        {
            if (Role == UserRole.MANAGER)
            {
                return ManagedDepartments;
            }
            return new List<Department>();
        }

        /// <summary>
        /// Get all departments accessible to this user based on role
        /// </summary>
        public ICollection<Department> GetAccessibleDepartments()
        {
            // Will be implemented after department relationships are added
            return new List<Department>();
        }

        /// <summary>
        /// Check if this user can evaluate the target user
        /// </summary>
        public bool CanEvaluateUser(ApplicationUser targetUser)
        {
            // Will be implemented after department relationships are added
            return false;
        }

        /// <summary>
        /// Soft delete the user
        /// </summary>
        public void SoftDelete()
        {
            IsDeleted = true;
            DeletedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Restore a soft deleted user
        /// </summary>
        public void Restore()
        {
            IsDeleted = false;
            DeletedAt = null;
        }
    }
}
