@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@inherits LocalizedComponentBase

<div class="page-header @GetLayoutClass() @CssClass">
    @if (ShowBreadcrumb && BreadcrumbItems?.Any() == true)
    {
        <nav aria-label="@L("Breadcrumb", "مسار التنقل")">
            <ol class="breadcrumb">
                @foreach (var item in BreadcrumbItems)
                {
                    @if (item.IsActive)
                    {
                        <li class="breadcrumb-item active" aria-current="page">
                            @item.Text
                        </li>
                    }
                    else
                    {
                        <li class="breadcrumb-item">
                            <a href="@item.Url" class="text-decoration-none">
                                @item.Text
                            </a>
                        </li>
                    }
                }
            </ol>
        </nav>
    }

    <div class="d-flex justify-content-between align-items-start flex-wrap">
        <div class="flex-grow-1">
            <h1 class="@GetTitleSizeClass() mb-2">
                @if (!string.IsNullOrEmpty(Icon))
                {
                    <i class="@Icon @GetMarginEnd() text-primary"></i>
                }
                @Title
            </h1>
            
            @if (!string.IsNullOrEmpty(Description))
            {
                <p class="lead mb-0">@Description</p>
            }
            
            @if (ChildContent != null)
            {
                <div class="mt-3">
                    @ChildContent
                </div>
            }
        </div>

        @if (Actions != null)
        {
            <div class="@GetMarginStart() mt-2 mt-md-0">
                @Actions
            </div>
        }
    </div>

    @if (Stats != null)
    {
        <div class="row mt-4">
            @Stats
        </div>
    }
</div>

@code {
    /// <summary>
    /// Page title
    /// </summary>
    [Parameter, EditorRequired] public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Page description
    /// </summary>
    [Parameter] public string? Description { get; set; }

    /// <summary>
    /// Icon class for the title
    /// </summary>
    [Parameter] public string? Icon { get; set; }

    /// <summary>
    /// Title size (h1, h2, h3, etc.)
    /// </summary>
    [Parameter] public string TitleSize { get; set; } = "h1";

    /// <summary>
    /// Whether to show breadcrumb navigation
    /// </summary>
    [Parameter] public bool ShowBreadcrumb { get; set; } = true;

    /// <summary>
    /// Breadcrumb items
    /// </summary>
    [Parameter] public List<BreadcrumbItem>? BreadcrumbItems { get; set; }

    /// <summary>
    /// Action buttons content
    /// </summary>
    [Parameter] public RenderFragment? Actions { get; set; }

    /// <summary>
    /// Statistics content
    /// </summary>
    [Parameter] public RenderFragment? Stats { get; set; }

    /// <summary>
    /// Additional content
    /// </summary>
    [Parameter] public RenderFragment? ChildContent { get; set; }

    /// <summary>
    /// Custom CSS class
    /// </summary>
    [Parameter] public string CssClass { get; set; } = "";

    private string GetTitleSizeClass()
    {
        return TitleSize switch
        {
            "h1" => "h1",
            "h2" => "h2",
            "h3" => "h3",
            "h4" => "h4",
            "h5" => "h5",
            "h6" => "h6",
            _ => "h1"
        };
    }

    public class BreadcrumbItem
    {
        public string Text { get; set; } = string.Empty;
        public string? Url { get; set; }
        public bool IsActive { get; set; }

        public BreadcrumbItem(string text, string? url = null, bool isActive = false)
        {
            Text = text;
            Url = url;
            IsActive = isActive;
        }
    }
}

<style>
    .page-header {
        background: var(--bg-primary);
        border-radius: var(--radius-lg);
        padding: var(--spacing-xl);
        margin-bottom: var(--spacing-xl);
        box-shadow: var(--shadow-md);
        border: 1px solid var(--border-color);
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
    }

    .page-header h1,
    .page-header h2,
    .page-header h3,
    .page-header h4,
    .page-header h5,
    .page-header h6 {
        color: var(--text-primary);
        font-weight: 700;
        margin-bottom: var(--spacing-sm);
    }

    .page-header .lead {
        color: var(--text-secondary);
        font-size: 1.1rem;
        font-weight: 400;
    }

    .page-header .breadcrumb {
        background: transparent;
        padding: 0;
        margin-bottom: var(--spacing-md);
    }

    .page-header .breadcrumb-item {
        font-size: 0.875rem;
        color: var(--text-muted);
    }

    .page-header .breadcrumb-item.active {
        color: var(--text-primary);
        font-weight: 500;
    }

    .page-header .breadcrumb-item a {
        color: var(--text-muted);
        transition: color var(--transition-normal);
    }

    .page-header .breadcrumb-item a:hover {
        color: var(--primary-color);
    }

    /* RTL Support */
    .rtl-layout .page-header .breadcrumb-item + .breadcrumb-item::before {
        content: "‹";
        float: right;
        padding-right: 0;
        padding-left: 0.5rem;
    }

    /* Responsive adjustments */
    @@media (max-width: 768px) {
        .page-header {
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .page-header h1 {
            font-size: 1.75rem;
        }

        .page-header .lead {
            font-size: 1rem;
        }

        .page-header .d-flex {
            flex-direction: column;
            align-items: stretch !important;
        }

        .page-header .breadcrumb {
            margin-bottom: var(--spacing-sm);
        }
    }
</style>
