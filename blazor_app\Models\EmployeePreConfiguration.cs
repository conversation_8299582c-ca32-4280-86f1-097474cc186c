using System.ComponentModel.DataAnnotations;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Model for pre-configuring employee role and department assignments.
    /// Allows Super Admin to set up automatic assignments for specific Employee IDs.
    /// </summary>
    public class EmployeePreConfiguration : BaseModel
    {
        /// <summary>
        /// Employee ID that this configuration applies to
        /// </summary>
        [Required]
        [StringLength(50)]
        [Display(Name = "Employee ID")]
        public string EmployeeId { get; set; } = string.Empty;

        /// <summary>
        /// Pre-configured role for this employee
        /// </summary>
        [Required]
        [Display(Name = "Assigned Role")]
        public UserRole AssignedRole { get; set; } = UserRole.EMPLOYEE;

        /// <summary>
        /// Pre-configured primary department ID
        /// </summary>
        [Display(Name = "Primary Department")]
        public int? PrimaryDepartmentId { get; set; }

        /// <summary>
        /// Primary department navigation property
        /// </summary>
        public virtual Department? PrimaryDepartment { get; set; }

        /// <summary>
        /// Whether this employee should be assigned as manager of their primary department
        /// </summary>
        [Display(Name = "Is Department Manager")]
        public bool IsDepartmentManager { get; set; } = false;

        /// <summary>
        /// Whether this employee should be assigned as supervisor in their primary department
        /// </summary>
        [Display(Name = "Is Department Supervisor")]
        public bool IsDepartmentSupervisor { get; set; } = false;

        /// <summary>
        /// Additional departments this employee should be assigned to
        /// </summary>
        public virtual ICollection<EmployeePreConfigurationDepartment> AdditionalDepartments { get; set; } = new List<EmployeePreConfigurationDepartment>();

        /// <summary>
        /// User who created this configuration
        /// </summary>
        [Required]
        public string CreatedByUserId { get; set; } = string.Empty;

        /// <summary>
        /// User who created this configuration (navigation property)
        /// </summary>
        public virtual ApplicationUser CreatedByUser { get; set; } = null!;

        /// <summary>
        /// User who last updated this configuration
        /// </summary>
        public string? UpdatedByUserId { get; set; }

        /// <summary>
        /// User who last updated this configuration (navigation property)
        /// </summary>
        public virtual ApplicationUser? UpdatedByUser { get; set; }

        /// <summary>
        /// Notes about this configuration
        /// </summary>
        [StringLength(1000)]
        [Display(Name = "Configuration Notes")]
        public string? Notes { get; set; }

        /// <summary>
        /// Whether this configuration is currently active
        /// </summary>
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Date when this configuration becomes effective
        /// </summary>
        [Display(Name = "Effective Date")]
        public DateTime EffectiveDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Date when this configuration expires (optional)
        /// </summary>
        [Display(Name = "Expiry Date")]
        public DateTime? ExpiryDate { get; set; }

        /// <summary>
        /// Check if this configuration is currently effective
        /// </summary>
        public bool IsCurrentlyEffective()
        {
            var now = DateTime.UtcNow;
            return IsActive &&
                   !IsDeleted &&
                   EffectiveDate <= now &&
                   (ExpiryDate == null || ExpiryDate > now);
        }

        /// <summary>
        /// Get display name for the assigned role
        /// </summary>
        public string GetRoleDisplayName(string language = "en")
        {
            return AssignedRole switch
            {
                UserRole.SUPER_ADMIN => language == "ar" ? "مدير النظام الرئيسي" : "Super Admin",
                UserRole.MANAGER => language == "ar" ? "المدير" : "Manager",
                UserRole.SUPERVISOR => language == "ar" ? "المسؤول المباشر" : "Supervisor",
                UserRole.EXCELLENCE_TEAM => language == "ar" ? "فريق التميز" : "Excellence Team",
                UserRole.EMPLOYEE => language == "ar" ? "الموظفين" : "Employee",
                _ => AssignedRole.ToString()
            };
        }

        public override string ToString()
        {
            return $"Employee {EmployeeId} -> {AssignedRole} in {PrimaryDepartment?.NameEn ?? "No Department"}";
        }
    }

    /// <summary>
    /// Junction table for additional department assignments in employee pre-configuration
    /// </summary>
    public class EmployeePreConfigurationDepartment : BaseModel
    {
        /// <summary>
        /// Employee pre-configuration ID
        /// </summary>
        [Required]
        public int EmployeePreConfigurationId { get; set; }

        /// <summary>
        /// Employee pre-configuration navigation property
        /// </summary>
        public virtual EmployeePreConfiguration EmployeePreConfiguration { get; set; } = null!;

        /// <summary>
        /// Department ID
        /// </summary>
        [Required]
        public int DepartmentId { get; set; }

        /// <summary>
        /// Department navigation property
        /// </summary>
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Role in this additional department
        /// </summary>
        [Required]
        [Display(Name = "Role in Department")]
        public DepartmentRole RoleInDepartment { get; set; } = DepartmentRole.EMPLOYEE;

        /// <summary>
        /// Whether this is a management role in this department
        /// </summary>
        [Display(Name = "Is Management Role")]
        public bool IsManagementRole { get; set; } = false;

        public override string ToString()
        {
            return $"{Department?.NameEn} - {RoleInDepartment}";
        }
    }
}