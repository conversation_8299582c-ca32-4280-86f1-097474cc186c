using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service for calculating comprehensive employee evaluations
    /// </summary>
    public interface IEvaluationCalculationService
    {
        Task<ComprehensiveEvaluation> CalculateEmployeeEvaluationAsync(string employeeId, string evaluationPeriod);
        Task<List<ComprehensiveEvaluation>> CalculateDepartmentEvaluationsAsync(int departmentId, string evaluationPeriod);
        Task<List<ComprehensiveEvaluation>> CalculateAllEvaluationsAsync(string evaluationPeriod);
        Task<bool> RecalculateRankingsAsync(string evaluationPeriod);
    }

    public class EvaluationCalculationService : IEvaluationCalculationService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<EvaluationCalculationService> _logger;

        public EvaluationCalculationService(
            ApplicationDbContext context,
            ILogger<EvaluationCalculationService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Calculate comprehensive evaluation for a single employee
        /// </summary>
        public async Task<ComprehensiveEvaluation> CalculateEmployeeEvaluationAsync(string employeeId, string evaluationPeriod)
        {
            try
            {
                // Get employee data
                var employee = await _context.Users.FindAsync(employeeId);
                if (employee == null)
                    throw new ArgumentException($"Employee with ID {employeeId} not found");

                // Get work volume data
                var workVolumeData = await _context.WorkVolumeData
                    .FirstOrDefaultAsync(w => w.EmployeeId == employeeId && w.EvaluationPeriod == evaluationPeriod);

                // Get department work volume total
                var departmentTotal = await _context.DepartmentWorkVolumeTotals
                    .FirstOrDefaultAsync(d => d.DepartmentId == employee.PrimaryDepartmentId && d.EvaluationPeriod == evaluationPeriod);

                // Get attendance data
                var attendanceData = await _context.AttendanceData
                    .FirstOrDefaultAsync(a => a.EmployeeId == employeeId && a.EvaluationPeriod == evaluationPeriod);

                // Get supervisor evaluation
                var supervisorEvaluation = await _context.SupervisorEvaluations
                    .FirstOrDefaultAsync(s => s.EmployeeId == employeeId && s.EvaluationPeriod == evaluationPeriod);

                // Calculate work volume component (60% weight)
                decimal workVolumePercentage = 0;
                decimal workVolumeScore = 0;

                if (workVolumeData != null && departmentTotal != null && departmentTotal.TotalDepartmentWork > 0)
                {
                    // Calculate employee work percentage within department
                    workVolumePercentage = workVolumeData.TotalEmployeeWork / departmentTotal.TotalDepartmentWork;

                    // Get highest work percentage in department for normalization
                    var highestWorkPercentage = await GetHighestWorkPercentageInDepartmentAsync(employee.PrimaryDepartmentId ?? 0, evaluationPeriod);

                    if (highestWorkPercentage > 0)
                    {
                        workVolumeScore = (workVolumePercentage / highestWorkPercentage) * 0.6m;
                    }
                }

                // Calculate attendance component (20% weight)
                decimal attendancePercentage = 0;
                decimal attendanceScore = 0;

                if (attendanceData != null && attendanceData.TotalWorkingDays > 0)
                {
                    attendancePercentage = attendanceData.AttendancePercentage;

                    // Get highest attendance rate for normalization
                    var highestAttendanceRate = await GetHighestAttendanceRateAsync(evaluationPeriod);

                    if (highestAttendanceRate > 0)
                    {
                        attendanceScore = (attendancePercentage / highestAttendanceRate) * 0.2m;
                    }
                }

                // Calculate supervisor evaluation component (20% weight)
                decimal supervisorScore = 0;

                if (supervisorEvaluation != null)
                {
                    supervisorScore = supervisorEvaluation.WeightedScore;
                }

                // Calculate final total score
                decimal totalScore = workVolumeScore + attendanceScore + supervisorScore;

                // Create or update comprehensive evaluation
                var comprehensiveEvaluation = await _context.ComprehensiveEvaluations
                    .FirstOrDefaultAsync(c => c.EmployeeId == employeeId && c.EvaluationPeriod == evaluationPeriod);

                if (comprehensiveEvaluation == null)
                {
                    comprehensiveEvaluation = new ComprehensiveEvaluation
                    {
                        EmployeeId = employeeId,
                        DepartmentId = employee.PrimaryDepartmentId ?? 0,
                        EvaluationPeriod = evaluationPeriod,
                        CalculatedBy = "System" // This should be the current user
                    };
                    _context.ComprehensiveEvaluations.Add(comprehensiveEvaluation);
                }

                // Update values
                comprehensiveEvaluation.WorkVolumePercentage = workVolumePercentage;
                comprehensiveEvaluation.WorkVolumeScore = workVolumeScore;
                comprehensiveEvaluation.AttendancePercentage = attendancePercentage;
                comprehensiveEvaluation.AttendanceScore = attendanceScore;
                comprehensiveEvaluation.SupervisorScore = supervisorScore;
                comprehensiveEvaluation.TotalScore = totalScore;
                comprehensiveEvaluation.CalculatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                _logger.LogInformation($"Calculated evaluation for employee {employeeId} in period {evaluationPeriod}. Total score: {totalScore:F4}");

                return comprehensiveEvaluation;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error calculating evaluation for employee {employeeId} in period {evaluationPeriod}");
                throw;
            }
        }

        /// <summary>
        /// Calculate evaluations for all employees in a department
        /// </summary>
        public async Task<List<ComprehensiveEvaluation>> CalculateDepartmentEvaluationsAsync(int departmentId, string evaluationPeriod)
        {
            var employees = await _context.Users
                .Where(u => u.PrimaryDepartmentId == departmentId && u.IsActive && !u.IsDeleted)
                .ToListAsync();

            var evaluations = new List<ComprehensiveEvaluation>();

            foreach (var employee in employees)
            {
                var evaluation = await CalculateEmployeeEvaluationAsync(employee.Id, evaluationPeriod);
                evaluations.Add(evaluation);
            }

            // Recalculate department rankings
            await RecalculateDepartmentRankingsAsync(departmentId, evaluationPeriod);

            return evaluations;
        }

        /// <summary>
        /// Calculate evaluations for all employees across all departments
        /// </summary>
        public async Task<List<ComprehensiveEvaluation>> CalculateAllEvaluationsAsync(string evaluationPeriod)
        {
            var departments = await _context.Departments.Where(d => d.IsActive).ToListAsync();
            var allEvaluations = new List<ComprehensiveEvaluation>();

            foreach (var department in departments)
            {
                var departmentEvaluations = await CalculateDepartmentEvaluationsAsync(department.Id, evaluationPeriod);
                allEvaluations.AddRange(departmentEvaluations);
            }

            // Recalculate overall rankings
            await RecalculateRankingsAsync(evaluationPeriod);

            return allEvaluations;
        }

        /// <summary>
        /// Recalculate rankings for all evaluations in a period
        /// </summary>
        public async Task<bool> RecalculateRankingsAsync(string evaluationPeriod)
        {
            try
            {
                // Recalculate overall rankings
                var allEvaluations = await _context.ComprehensiveEvaluations
                    .Where(e => e.EvaluationPeriod == evaluationPeriod)
                    .OrderByDescending(e => e.TotalScore)
                    .ToListAsync();

                for (int i = 0; i < allEvaluations.Count; i++)
                {
                    allEvaluations[i].OverallRank = i + 1;
                }

                // Recalculate department rankings
                var departments = await _context.Departments.Where(d => d.IsActive).ToListAsync();

                foreach (var department in departments)
                {
                    await RecalculateDepartmentRankingsAsync(department.Id, evaluationPeriod);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error recalculating rankings for period {evaluationPeriod}");
                return false;
            }
        }

        /// <summary>
        /// Get the highest work percentage in a department for normalization
        /// </summary>
        private async Task<decimal> GetHighestWorkPercentageInDepartmentAsync(int departmentId, string evaluationPeriod)
        {
            var departmentTotal = await _context.DepartmentWorkVolumeTotals
                .FirstOrDefaultAsync(d => d.DepartmentId == departmentId && d.EvaluationPeriod == evaluationPeriod);

            if (departmentTotal == null || departmentTotal.TotalDepartmentWork == 0)
                return 0;

            var workVolumeData = await _context.WorkVolumeData
                .Where(w => w.DepartmentId == departmentId && w.EvaluationPeriod == evaluationPeriod)
                .ToListAsync();

            if (!workVolumeData.Any())
                return 0;

            return workVolumeData.Max(w => w.TotalEmployeeWork / departmentTotal.TotalDepartmentWork);
        }

        /// <summary>
        /// Get the highest attendance rate for normalization
        /// </summary>
        private async Task<decimal> GetHighestAttendanceRateAsync(string evaluationPeriod)
        {
            var attendanceData = await _context.AttendanceData
                .Where(a => a.EvaluationPeriod == evaluationPeriod && a.TotalWorkingDays > 0)
                .ToListAsync();

            if (!attendanceData.Any())
                return 0;

            return attendanceData.Max(a => a.AttendancePercentage);
        }

        /// <summary>
        /// Recalculate department rankings
        /// </summary>
        private async Task RecalculateDepartmentRankingsAsync(int departmentId, string evaluationPeriod)
        {
            var departmentEvaluations = await _context.ComprehensiveEvaluations
                .Where(e => e.DepartmentId == departmentId && e.EvaluationPeriod == evaluationPeriod)
                .OrderByDescending(e => e.TotalScore)
                .ToListAsync();

            for (int i = 0; i < departmentEvaluations.Count; i++)
            {
                departmentEvaluations[i].DepartmentRank = i + 1;
            }
        }
    }
}
