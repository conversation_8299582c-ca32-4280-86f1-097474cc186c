using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service for seeding sample evaluation data for testing
    /// </summary>
    public interface IEvaluationDataSeedingService
    {
        Task SeedSampleDataAsync(string evaluationPeriod, int departmentId);
        Task SeedCurrentMonthDataAsync();
    }

    public class EvaluationDataSeedingService : IEvaluationDataSeedingService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<EvaluationDataSeedingService> _logger;

        public EvaluationDataSeedingService(
            ApplicationDbContext context,
            ILogger<EvaluationDataSeedingService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task SeedCurrentMonthDataAsync()
        {
            var currentPeriod = DateTime.Now.ToString("yyyy-MM");
            
            // Get the first active department
            var department = await _context.Departments.FirstOrDefaultAsync(d => d.IsActive);
            if (department == null)
            {
                _logger.LogWarning("No active departments found for seeding data");
                return;
            }

            await SeedSampleDataAsync(currentPeriod, department.Id);
        }

        public async Task SeedSampleDataAsync(string evaluationPeriod, int departmentId)
        {
            try
            {
                _logger.LogInformation($"Seeding sample evaluation data for period {evaluationPeriod}, department {departmentId}");

                // Check if data already exists
                var existingData = await _context.WorkVolumeData
                    .AnyAsync(w => w.EvaluationPeriod == evaluationPeriod && w.DepartmentId == departmentId);

                if (existingData)
                {
                    _logger.LogInformation("Sample data already exists for this period and department");
                    return;
                }

                // Get employees in the department
                var employees = await _context.Users
                    .Where(u => u.PrimaryDepartmentId == departmentId && u.IsActive && !u.IsDeleted)
                    .ToListAsync();

                if (!employees.Any())
                {
                    _logger.LogWarning($"No employees found in department {departmentId}");
                    return;
                }

                // Seed monthly working days
                await SeedMonthlyWorkingDays(evaluationPeriod);

                // Seed department work volume totals
                await SeedDepartmentWorkVolumeTotals(evaluationPeriod, departmentId);

                // Seed employee work volume data
                await SeedEmployeeWorkVolumeData(evaluationPeriod, departmentId, employees);

                // Seed attendance data
                await SeedAttendanceData(evaluationPeriod, departmentId, employees);

                // Seed supervisor evaluations
                await SeedSupervisorEvaluations(evaluationPeriod, departmentId, employees);

                await _context.SaveChangesAsync();

                _logger.LogInformation($"Successfully seeded sample data for {employees.Count} employees");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error seeding sample data for period {evaluationPeriod}, department {departmentId}");
                throw;
            }
        }

        private async Task SeedMonthlyWorkingDays(string evaluationPeriod)
        {
            var existing = await _context.MonthlyWorkingDays
                .FirstOrDefaultAsync(m => m.EvaluationPeriod == evaluationPeriod);

            if (existing == null)
            {
                var monthlyDays = new MonthlyWorkingDays
                {
                    EvaluationPeriod = evaluationPeriod,
                    TotalWorkingDays = 22, // Typical working days in a month
                    Holidays = 2,
                    Weekends = 8,
                    Comments = "Sample data for testing",
                    ConfiguredBy = "System"
                };

                _context.MonthlyWorkingDays.Add(monthlyDays);
            }
        }

        private async Task SeedDepartmentWorkVolumeTotals(string evaluationPeriod, int departmentId)
        {
            var existing = await _context.DepartmentWorkVolumeTotals
                .FirstOrDefaultAsync(d => d.DepartmentId == departmentId && d.EvaluationPeriod == evaluationPeriod);

            if (existing == null)
            {
                var departmentTotal = new DepartmentWorkVolumeTotal
                {
                    DepartmentId = departmentId,
                    EvaluationPeriod = evaluationPeriod,
                    TotalQualityProgramWork = 1000m,
                    TotalOracleWork = 800m,
                    TotalDocumentedWork = 600m,
                    RecordedBy = "System"
                };

                _context.DepartmentWorkVolumeTotals.Add(departmentTotal);
            }
        }

        private async Task SeedEmployeeWorkVolumeData(string evaluationPeriod, int departmentId, List<ApplicationUser> employees)
        {
            var random = new Random(42); // Fixed seed for consistent results

            foreach (var employee in employees)
            {
                var existing = await _context.WorkVolumeData
                    .FirstOrDefaultAsync(w => w.EmployeeId == employee.Id && w.EvaluationPeriod == evaluationPeriod);

                if (existing == null)
                {
                    var workData = new WorkVolumeData
                    {
                        EmployeeId = employee.Id,
                        DepartmentId = departmentId,
                        EvaluationPeriod = evaluationPeriod,
                        QualityProgramWork = (decimal)(random.NextDouble() * 200 + 50), // 50-250
                        OracleWork = (decimal)(random.NextDouble() * 150 + 30), // 30-180
                        DocumentedWork = (decimal)(random.NextDouble() * 100 + 20), // 20-120
                        Comments = "Sample work volume data",
                        RecordedBy = "System"
                    };

                    _context.WorkVolumeData.Add(workData);
                }
            }
        }

        private async Task SeedAttendanceData(string evaluationPeriod, int departmentId, List<ApplicationUser> employees)
        {
            var random = new Random(42);

            foreach (var employee in employees)
            {
                var existing = await _context.AttendanceData
                    .FirstOrDefaultAsync(a => a.EmployeeId == employee.Id && a.EvaluationPeriod == evaluationPeriod);

                if (existing == null)
                {
                    var attendanceData = new AttendanceData
                    {
                        EmployeeId = employee.Id,
                        DepartmentId = departmentId,
                        EvaluationPeriod = evaluationPeriod,
                        AttendanceDays = random.Next(18, 23), // 18-22 days out of 22
                        TotalWorkingDays = 22,
                        AbsenceDays = random.Next(0, 4),
                        LateArrivals = random.Next(0, 5),
                        EarlyDepartures = random.Next(0, 3),
                        Comments = "Sample attendance data",
                        RecordedBy = "System"
                    };

                    _context.AttendanceData.Add(attendanceData);
                }
            }
        }

        private async Task SeedSupervisorEvaluations(string evaluationPeriod, int departmentId, List<ApplicationUser> employees)
        {
            var random = new Random(42);
            
            // Get a supervisor (first manager or admin)
            var supervisor = await _context.Users
                .FirstOrDefaultAsync(u => (u.Role == UserRole.MANAGER || u.Role == UserRole.SUPER_ADMIN) && u.IsActive);

            if (supervisor == null)
            {
                _logger.LogWarning("No supervisor found for seeding supervisor evaluations");
                return;
            }

            foreach (var employee in employees)
            {
                var existing = await _context.SupervisorEvaluations
                    .FirstOrDefaultAsync(s => s.EmployeeId == employee.Id && s.EvaluationPeriod == evaluationPeriod);

                if (existing == null)
                {
                    var evaluation = new SupervisorEvaluation
                    {
                        EmployeeId = employee.Id,
                        SupervisorId = supervisor.Id,
                        DepartmentId = departmentId,
                        EvaluationPeriod = evaluationPeriod,
                        EfficiencyAndQualityScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        LeadershipAbilityScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        PlanningAndInnovationScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        TeamworkParticipationScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        ResponsibilityAndPressureScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        EmergencyHandlingScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        GeneralBehaviorScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        RelationshipWithSuperiorsScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        DisciplineAndCommitmentScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        WorkDevelopmentScore = (decimal)(random.NextDouble() * 2 + 3), // 3-5
                        Comments = "Sample supervisor evaluation",
                        Status = EvaluationStatus.APPROVED
                    };

                    _context.SupervisorEvaluations.Add(evaluation);
                }
            }
        }
    }
}
