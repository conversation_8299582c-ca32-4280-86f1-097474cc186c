using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Represents attendance data for an employee in a specific month
    /// </summary>
    public class AttendanceData : TimeStampedModel
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Employee ID
        /// </summary>
        [Required]
        public string EmployeeId { get; set; } = string.Empty;

        /// <summary>
        /// Reference to the employee
        /// </summary>
        [ForeignKey(nameof(EmployeeId))]
        public virtual ApplicationUser Employee { get; set; } = null!;

        /// <summary>
        /// Department ID
        /// </summary>
        [Required]
        public int DepartmentId { get; set; }

        /// <summary>
        /// Reference to the department
        /// </summary>
        [ForeignKey(nameof(DepartmentId))]
        public virtual Department Department { get; set; } = null!;

        /// <summary>
        /// Evaluation period (Year-Month format: 2025-01)
        /// </summary>
        [Required]
        [StringLength(7)]
        public string EvaluationPeriod { get; set; } = string.Empty;

        /// <summary>
        /// Number of days the employee attended
        /// عدد ايام حضور الموظف
        /// </summary>
        public int AttendanceDays { get; set; }

        /// <summary>
        /// Total working days in the month
        /// عدد ايام الحضور في الشهر
        /// </summary>
        public int TotalWorkingDays { get; set; }

        /// <summary>
        /// Number of absence days
        /// </summary>
        public int AbsenceDays { get; set; }

        /// <summary>
        /// Number of late arrivals
        /// </summary>
        public int LateArrivals { get; set; }

        /// <summary>
        /// Number of early departures
        /// </summary>
        public int EarlyDepartures { get; set; }

        /// <summary>
        /// Attendance percentage for this employee
        /// نسبة حضور الموظف
        /// </summary>
        [NotMapped]
        public decimal AttendancePercentage => TotalWorkingDays > 0 ? (decimal)AttendanceDays / TotalWorkingDays : 0;

        /// <summary>
        /// Comments or notes about attendance
        /// </summary>
        [StringLength(500)]
        public string? Comments { get; set; }

        /// <summary>
        /// Who recorded this data
        /// </summary>
        [Required]
        public string RecordedBy { get; set; } = string.Empty;

        /// <summary>
        /// When this data was recorded
        /// </summary>
        public DateTime RecordedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Represents monthly working days configuration
    /// </summary>
    public class MonthlyWorkingDays : TimeStampedModel
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Evaluation period (Year-Month format: 2025-01)
        /// </summary>
        [Required]
        [StringLength(7)]
        public string EvaluationPeriod { get; set; } = string.Empty;

        /// <summary>
        /// Total working days in this month
        /// </summary>
        public int TotalWorkingDays { get; set; }

        /// <summary>
        /// Number of holidays in this month
        /// </summary>
        public int Holidays { get; set; }

        /// <summary>
        /// Number of weekends in this month
        /// </summary>
        public int Weekends { get; set; }

        /// <summary>
        /// Comments about the month (special holidays, etc.)
        /// </summary>
        [StringLength(500)]
        public string? Comments { get; set; }

        /// <summary>
        /// Who configured this data
        /// </summary>
        [Required]
        public string ConfiguredBy { get; set; } = string.Empty;

        /// <summary>
        /// When this was configured
        /// </summary>
        public DateTime ConfiguredAt { get; set; } = DateTime.UtcNow;
    }
}
