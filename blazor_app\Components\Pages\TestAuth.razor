@page "/test-auth"
@rendermode InteractiveServer
@using Microsoft.AspNetCore.Components.Authorization
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Models
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore

@inject IEmployeeAuthenticationService EmployeeAuthService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject NavigationManager Navigation
@inject UserManager<ApplicationUser> UserManager

<PageTitle>Authentication Test</PageTitle>

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3>Authentication Test Page</h3>
                </div>
                <div class="card-body">
                    
                    <!-- Current Authentication State -->
                    <div class="mb-4">
                        <h5>Current Authentication State</h5>
                        <div class="alert alert-info">
                            <strong>Is Authenticated:</strong> @isAuthenticated<br>
                            <strong>User Identity:</strong> @(authState?.User?.Identity?.Name ?? "None")<br>
                            <strong>Employee ID:</strong> @(authState?.User?.FindFirst("EmployeeId")?.Value ?? "None")<br>
                            <strong>Role:</strong> @(authState?.User?.FindFirst("UserRole")?.Value ?? "None")
                        </div>
                    </div>

                    <!-- Test Login Form -->
                    <div class="mb-4">
                        <h5>Test Login</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Employee ID</label>
                                    <input type="text" class="form-control" @bind="testEmployeeId" placeholder="EMP001" />
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <input type="password" class="form-control" @bind="testPassword" placeholder="Password123!" />
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary" @onclick="TestLogin" disabled="@isTestingLogin">
                            @if (isTestingLogin)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            Test Login
                        </button>
                    </div>

                    <!-- Test Results -->
                    @if (!string.IsNullOrEmpty(testResult))
                    {
                        <div class="mb-4">
                            <h5>Test Results</h5>
                            <div class="alert @(testSuccess ? "alert-success" : "alert-danger")">
                                @testResult
                            </div>
                        </div>
                    }

                    <!-- User Lookup Test -->
                    <div class="mb-4">
                        <h5>User Lookup Test</h5>
                        <div class="row">
                            <div class="col-md-8">
                                <input type="text" class="form-control" @bind="lookupEmployeeId" placeholder="Enter Employee ID to lookup" />
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-secondary" @onclick="LookupUser">Lookup User</button>
                            </div>
                        </div>
                        @if (lookupResult != null)
                        {
                            <div class="mt-3 alert alert-info">
                                <strong>User Found:</strong> @(lookupResult != null ? "Yes" : "No")<br>
                                @if (lookupResult != null)
                                {
                                    <strong>Employee ID:</strong> @lookupResult.EmployeeId<br>
                                    <strong>English Name:</strong> @lookupResult.EnglishName<br>
                                    <strong>Arabic Name:</strong> @lookupResult.ArabicName<br>
                                    <strong>Role:</strong> @lookupResult.Role<br>
                                    <strong>Is Active:</strong> @lookupResult.IsActive<br>
                                    <strong>Is Deleted:</strong> @lookupResult.IsDeleted
                                }
                            </div>
                        }
                    </div>

                    <!-- Manual User Creation -->
                    <div class="mb-4">
                        <h5>Manual User Creation</h5>
                        <button class="btn btn-primary" @onclick="CreateTestUser" disabled="@isCreatingUser">
                            @if (isCreatingUser)
                            {
                                <span class="spinner-border spinner-border-sm me-2"></span>
                            }
                            Create Test User (EMP001)
                        </button>
                        @if (!string.IsNullOrEmpty(createUserResult))
                        {
                            <div class="mt-2 alert @(createUserSuccess ? "alert-success" : "alert-danger")">
                                @createUserResult
                            </div>
                        }
                    </div>

                    <!-- Actions -->
                    <div class="mb-4">
                        <h5>Actions</h5>
                        <button class="btn btn-info me-2" @onclick="RefreshAuthState">Refresh Auth State</button>
                        <button class="btn btn-warning me-2" @onclick="GoToLogin">Go to Login Page</button>
                        <button class="btn btn-success me-2" @onclick="GoToDashboard">Go to Dashboard</button>
                        @if (isAuthenticated)
                        {
                            <button class="btn btn-danger" @onclick="TestLogout">Logout</button>
                        }
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private AuthenticationState? authState;
    private bool isAuthenticated = false;
    private string testEmployeeId = "EMP001";
    private string testPassword = "Password123!";
    private bool isTestingLogin = false;
    private string testResult = string.Empty;
    private bool testSuccess = false;
    private string lookupEmployeeId = "EMP001";
    private ApplicationUser? lookupResult;
    private bool isCreatingUser = false;
    private string createUserResult = string.Empty;
    private bool createUserSuccess = false;

    protected override async Task OnInitializedAsync()
    {
        await RefreshAuthState();
    }

    private async Task RefreshAuthState()
    {
        authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        isAuthenticated = authState.User.Identity?.IsAuthenticated == true;
        StateHasChanged();
    }

    private async Task TestLogin()
    {
        isTestingLogin = true;
        testResult = string.Empty;
        StateHasChanged();

        try
        {
            var result = await EmployeeAuthService.SignInWithEmployeeIdAsync(testEmployeeId, testPassword, false);
            
            if (result.Succeeded)
            {
                testResult = "Login successful! Refreshing authentication state...";
                testSuccess = true;
                
                // Wait a moment and refresh auth state
                await Task.Delay(500);
                await RefreshAuthState();
                
                testResult += $"\nAuthentication state updated. Is Authenticated: {isAuthenticated}";
            }
            else if (result.IsLockedOut)
            {
                testResult = "Login failed: Account is locked out.";
                testSuccess = false;
            }
            else
            {
                testResult = "Login failed: Invalid credentials.";
                testSuccess = false;
            }
        }
        catch (Exception ex)
        {
            testResult = $"Login error: {ex.Message}";
            testSuccess = false;
        }
        finally
        {
            isTestingLogin = false;
            StateHasChanged();
        }
    }

    private async Task LookupUser()
    {
        try
        {
            lookupResult = await EmployeeAuthService.FindByEmployeeIdAsync(lookupEmployeeId);
            StateHasChanged();
        }
        catch (Exception ex)
        {
            testResult = $"Lookup error: {ex.Message}";
            testSuccess = false;
            StateHasChanged();
        }
    }

    private async Task TestLogout()
    {
        try
        {
            await EmployeeAuthService.SignOutAsync();
            await Task.Delay(500);
            await RefreshAuthState();
            testResult = "Logout successful!";
            testSuccess = true;
        }
        catch (Exception ex)
        {
            testResult = $"Logout error: {ex.Message}";
            testSuccess = false;
        }
        StateHasChanged();
    }

    private void GoToLogin()
    {
        Navigation.NavigateTo("/login");
    }

    private void GoToDashboard()
    {
        Navigation.NavigateTo("/dashboard");
    }

    private async Task CreateTestUser()
    {
        isCreatingUser = true;
        createUserResult = string.Empty;
        StateHasChanged();

        try
        {
            // Check if user already exists
            var existingUser = await UserManager.Users.FirstOrDefaultAsync(u => u.EmployeeId == "EMP001");
            if (existingUser != null)
            {
                createUserResult = $"User EMP001 already exists!\nEmployee ID: {existingUser.EmployeeId}\nName: {existingUser.EnglishName}";
                createUserSuccess = true;
                return;
            }

            // Create new user directly
            var user = new ApplicationUser
            {
                UserName = "<EMAIL>",
                Email = "<EMAIL>",
                EmployeeId = "EMP001",
                EnglishName = "System Administrator",
                ArabicName = "مدير النظام",
                Role = UserRole.SUPER_ADMIN,
                IsActive = true,
                PreferredLanguage = "en",
                EmailConfirmed = true
            };

            var result = await UserManager.CreateAsync(user, "Password123!");

            if (result.Succeeded)
            {
                createUserResult = $"Test user created successfully!\nEmployee ID: {user.EmployeeId}\nName: {user.EnglishName}\nEmail: {user.Email}";
                createUserSuccess = true;
            }
            else
            {
                var errors = result.Errors?.Select(e => e.Description) ?? new[] { "Unknown error occurred" };
                createUserResult = $"Failed to create user: {string.Join(", ", errors)}";
                createUserSuccess = false;
            }
        }
        catch (Exception ex)
        {
            createUserResult = $"Error creating user: {ex.Message}";
            createUserSuccess = false;
        }
        finally
        {
            isCreatingUser = false;
            StateHasChanged();
        }
    }
}
