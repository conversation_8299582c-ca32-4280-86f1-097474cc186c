using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Text.RegularExpressions;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service for Employee ID-based authentication and registration.
    /// Implements authentication using Employee ID instead of email with automatic role assignment.
    /// </summary>
    public class EmployeeAuthenticationService : IEmployeeAuthenticationService
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly IEmployeeManagementService _employeeManagementService;
        private readonly ILogger<EmployeeAuthenticationService> _logger;

        public EmployeeAuthenticationService(
            UserManager<ApplicationUser> userManager,
            SignInManager<ApplicationUser> signInManager,
            IEmployeeManagementService employeeManagementService,
            ILogger<EmployeeAuthenticationService> logger)
        {
            _userManager = userManager;
            _signInManager = signInManager;
            _employeeManagementService = employeeManagementService;
            _logger = logger;
        }

        public async Task<SignInResult> SignInWithEmployeeIdAsync(string employeeId, string password, bool rememberMe = false, bool lockoutOnFailure = true)
        {
            try
            {
                // Find user by Employee ID
                var user = await FindByEmployeeIdAsync(employeeId);

                if (user == null)
                {
                    _logger.LogWarning($"Login attempt with non-existent Employee ID: {employeeId}");
                    return SignInResult.Failed;
                }

                // Check if user is active
                if (!user.IsActive || user.IsDeleted)
                {
                    _logger.LogWarning($"Login attempt with inactive/deleted Employee ID: {employeeId}");
                    return SignInResult.Failed;
                }

                // Check if user is locked out
                if (await _userManager.IsLockedOutAsync(user))
                {
                    _logger.LogWarning($"Login attempt with locked out Employee ID: {employeeId}");
                    return SignInResult.LockedOut;
                }

                // Verify password manually first
                var passwordValid = await _userManager.CheckPasswordAsync(user, password);
                if (!passwordValid)
                {
                    _logger.LogWarning($"Invalid password for Employee ID: {employeeId}");
                    await _userManager.AccessFailedAsync(user);
                    return SignInResult.Failed;
                }

                // Reset access failed count on successful password verification
                await _userManager.ResetAccessFailedCountAsync(user);

                // Add custom claims for Employee ID and other user properties
                var additionalClaims = new List<Claim>
                {
                    new Claim("EmployeeId", user.EmployeeId ?? ""),
                    new Claim("EnglishName", user.EnglishName ?? ""),
                    new Claim("ArabicName", user.ArabicName ?? ""),
                    new Claim("UserRole", user.Role.ToString()),
                    new Claim("PreferredLanguage", user.PreferredLanguage ?? "en")
                };

                _logger.LogInformation($"Creating claims for user: {user.EmployeeId}");
                _logger.LogInformation($"Claims: EmployeeId={user.EmployeeId}, Role={user.Role}, Language={user.PreferredLanguage}");

                // Perform actual sign-in with additional claims for Blazor Server
                _logger.LogInformation($"Performing Blazor Server sign-in with claims for user: {user.EmployeeId}");
                await _signInManager.SignInWithClaimsAsync(user, rememberMe, additionalClaims);

                _logger.LogInformation($"Successful Blazor Server login for Employee ID: {employeeId}");

                return SignInResult.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error during sign-in for Employee ID: {employeeId}");
                return SignInResult.Failed;
            }
        }

        public async Task<(IdentityResult result, ApplicationUser? user)> RegisterWithEmployeeIdAsync(
            string employeeId, 
            string password, 
            string englishName, 
            string arabicName, 
            string email, 
            string preferredLanguage = "en")
        {
            try
            {
                // Validate Employee ID format
                if (!IsValidEmployeeIdFormat(employeeId))
                {
                    var error = IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "InvalidEmployeeId", 
                        Description = "Employee ID format is invalid." 
                    });
                    return (error, null);
                }

                // Check if Employee ID is available
                if (!await IsEmployeeIdAvailableAsync(employeeId))
                {
                    var error = IdentityResult.Failed(new IdentityError 
                    { 
                        Code = "DuplicateEmployeeId", 
                        Description = "Employee ID is already in use." 
                    });
                    return (error, null);
                }

                // Get role and department assignment for this Employee ID
                // Temporarily simplified - assign EMPLOYEE role to all new users
                var assignedRole = UserRole.EMPLOYEE;
                int? departmentId = null;

                // Create new user
                var user = new ApplicationUser
                {
                    UserName = email, // Use email as username for Identity compatibility
                    Email = email,
                    EmployeeId = employeeId,
                    EnglishName = englishName,
                    ArabicName = arabicName,
                    Role = assignedRole,
                    PrimaryDepartmentId = departmentId,
                    PreferredLanguage = preferredLanguage,
                    IsActive = true,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // Create user with password
                var result = await _userManager.CreateAsync(user, password);
                
                if (result.Succeeded)
                {
                    // Add user to role
                    await _userManager.AddToRoleAsync(user, assignedRole.ToString());

                    // Create user-department relationship if department is assigned
                    // Temporarily disabled to avoid circular dependency
                    // if (departmentId.HasValue)
                    // {
                    //     await _employeeManagementService.AssignRoleAndDepartmentAsync(user.Id, assignedRole, departmentId, user.Id);
                    // }

                    _logger.LogInformation($"User registered successfully: Employee ID {employeeId}, Role: {assignedRole}, Department: {departmentId}");
                }
                else
                {
                    _logger.LogWarning($"Failed to register user with Employee ID: {employeeId}. Errors: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                }

                return (result, result.Succeeded ? user : null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error during registration for Employee ID: {employeeId}");
                var error = IdentityResult.Failed(new IdentityError 
                { 
                    Code = "RegistrationError", 
                    Description = "An error occurred during registration." 
                });
                return (error, null);
            }
        }

        public async Task<bool> IsEmployeeIdAvailableAsync(string employeeId)
        {
            // Simplified check without using EmployeeManagementService
            var existingUser = await _userManager.Users.FirstOrDefaultAsync(u => u.EmployeeId == employeeId);
            return existingUser == null;
        }

        public async Task<ApplicationUser?> FindByEmployeeIdAsync(string employeeId)
        {
            // Simplified check without using EmployeeManagementService
            return await _userManager.Users.FirstOrDefaultAsync(u => u.EmployeeId == employeeId);
        }

        public bool IsValidEmployeeIdFormat(string employeeId)
        {
            if (string.IsNullOrWhiteSpace(employeeId))
                return false;

            // Basic validation - can be customized based on your Employee ID format requirements
            // Current validation: 3-50 characters, alphanumeric
            var regex = new Regex(@"^[A-Za-z0-9]{3,50}$");
            return regex.IsMatch(employeeId);
        }

        public async Task SignOutAsync()
        {
            await _signInManager.SignOutAsync();
            _logger.LogInformation("User signed out");
        }

        public async Task<SignInResult> PerformActualSignInAsync(string employeeId, string password, bool rememberMe = false)
        {
            try
            {
                _logger.LogInformation($"=== AUTHENTICATION DEBUG START ===");
                _logger.LogInformation($"Attempting login for Employee ID: '{employeeId}' (Length: {employeeId?.Length ?? 0})");
                _logger.LogInformation($"Password provided: {!string.IsNullOrEmpty(password)} (Length: {password?.Length ?? 0})");
                _logger.LogInformation($"Remember Me: {rememberMe}");

                // Find user by Employee ID
                var user = await FindByEmployeeIdAsync(employeeId);
                _logger.LogInformation($"User lookup result: {(user != null ? "FOUND" : "NOT FOUND")}");

                if (user == null)
                {
                    _logger.LogWarning($"Login attempt with non-existent Employee ID: {employeeId}");
                    _logger.LogInformation($"=== AUTHENTICATION DEBUG END (USER NOT FOUND) ===");
                    return SignInResult.Failed;
                }

                _logger.LogInformation($"User found - ID: {user.EmployeeId}, Email: {user.Email}, Active: {user.IsActive}, Deleted: {user.IsDeleted}");

                // Check if user is active
                if (!user.IsActive || user.IsDeleted)
                {
                    _logger.LogWarning($"Login attempt with inactive/deleted Employee ID: {employeeId}");
                    _logger.LogInformation($"=== AUTHENTICATION DEBUG END (USER INACTIVE) ===");
                    return SignInResult.Failed;
                }

                // Check if user is locked out
                var isLockedOut = await _userManager.IsLockedOutAsync(user);
                _logger.LogInformation($"User lockout status: {isLockedOut}");
                if (isLockedOut)
                {
                    _logger.LogWarning($"Login attempt with locked out Employee ID: {employeeId}");
                    _logger.LogInformation($"=== AUTHENTICATION DEBUG END (USER LOCKED OUT) ===");
                    return SignInResult.LockedOut;
                }

                // Verify password manually first
                _logger.LogInformation($"Checking password for user: {user.EmployeeId}");
                var passwordValid = await _userManager.CheckPasswordAsync(user, password);
                _logger.LogInformation($"Password validation result: {passwordValid}");

                if (!passwordValid)
                {
                    _logger.LogWarning($"Invalid password for Employee ID: {employeeId}");
                    await _userManager.AccessFailedAsync(user);
                    _logger.LogInformation($"=== AUTHENTICATION DEBUG END (INVALID PASSWORD) ===");
                    return SignInResult.Failed;
                }

                // Reset access failed count on successful password verification
                _logger.LogInformation($"Password valid! Resetting access failed count for user: {user.EmployeeId}");
                await _userManager.ResetAccessFailedCountAsync(user);

                // Add custom claims for Employee ID and other user properties
                var additionalClaims = new List<Claim>
                {
                    new Claim("EmployeeId", user.EmployeeId ?? ""),
                    new Claim("EnglishName", user.EnglishName ?? ""),
                    new Claim("ArabicName", user.ArabicName ?? ""),
                    new Claim("UserRole", user.Role.ToString()),
                    new Claim("PreferredLanguage", user.PreferredLanguage ?? "en")
                };

                _logger.LogInformation($"Creating claims for user: {user.EmployeeId}");
                _logger.LogInformation($"Claims: EmployeeId={user.EmployeeId}, Role={user.Role}, Language={user.PreferredLanguage}");

                // Perform actual sign-in with additional claims
                _logger.LogInformation($"Performing sign-in with claims for user: {user.EmployeeId}");
                await _signInManager.SignInWithClaimsAsync(user, rememberMe, additionalClaims);

                _logger.LogInformation($"Successful login for Employee ID: {employeeId}");
                _logger.LogInformation($"=== AUTHENTICATION DEBUG END (SUCCESS) ===");
                return SignInResult.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error during actual sign-in for Employee ID: {employeeId}");
                _logger.LogError($"Exception details: {ex.Message}");
                _logger.LogError($"Stack trace: {ex.StackTrace}");
                _logger.LogInformation($"=== AUTHENTICATION DEBUG END (EXCEPTION) ===");
                return SignInResult.Failed;
            }
        }

        public async Task<IdentityResult> ChangePasswordAsync(ApplicationUser user, string currentPassword, string newPassword)
        {
            try
            {
                var result = await _userManager.ChangePasswordAsync(user, currentPassword, newPassword);
                
                if (result.Succeeded)
                {
                    _logger.LogInformation($"Password changed successfully for Employee ID: {user.EmployeeId}");
                }
                else
                {
                    _logger.LogWarning($"Failed to change password for Employee ID: {user.EmployeeId}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error changing password for Employee ID: {user.EmployeeId}");
                return IdentityResult.Failed(new IdentityError 
                { 
                    Code = "PasswordChangeError", 
                    Description = "An error occurred while changing the password." 
                });
            }
        }

        public async Task<IdentityResult> ResetPasswordAsync(ApplicationUser user, string newPassword)
        {
            try
            {
                // Generate password reset token
                var token = await _userManager.GeneratePasswordResetTokenAsync(user);
                
                // Reset password using token
                var result = await _userManager.ResetPasswordAsync(user, token, newPassword);
                
                if (result.Succeeded)
                {
                    _logger.LogInformation($"Password reset successfully for Employee ID: {user.EmployeeId}");
                }
                else
                {
                    _logger.LogWarning($"Failed to reset password for Employee ID: {user.EmployeeId}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error resetting password for Employee ID: {user.EmployeeId}");
                return IdentityResult.Failed(new IdentityError 
                { 
                    Code = "PasswordResetError", 
                    Description = "An error occurred while resetting the password." 
                });
            }
        }

        public async Task<IdentityResult> LockUserAsync(ApplicationUser user, DateTimeOffset? lockoutEnd = null)
        {
            try
            {
                // Set lockout end date (default to 1 year if not specified)
                var lockoutEndDate = lockoutEnd ?? DateTimeOffset.UtcNow.AddYears(1);
                
                var result = await _userManager.SetLockoutEndDateAsync(user, lockoutEndDate);
                
                if (result.Succeeded)
                {
                    _logger.LogInformation($"User locked successfully: Employee ID {user.EmployeeId} until {lockoutEndDate}");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error locking user: Employee ID {user.EmployeeId}");
                return IdentityResult.Failed(new IdentityError 
                { 
                    Code = "LockUserError", 
                    Description = "An error occurred while locking the user." 
                });
            }
        }



        public async Task<bool> IsLockedOutAsync(ApplicationUser user)
        {
            return await _userManager.IsLockedOutAsync(user);
        }

        public async Task<DateTimeOffset?> GetLockoutEndDateAsync(ApplicationUser user)
        {
            return await _userManager.GetLockoutEndDateAsync(user);
        }

        public async Task<bool> UnlockUserAsync(string employeeId)
        {
            try
            {
                var user = await FindByEmployeeIdAsync(employeeId);
                if (user != null)
                {
                    // Reset lockout
                    await _userManager.SetLockoutEndDateAsync(user, null);
                    await _userManager.ResetAccessFailedCountAsync(user);
                    _logger.LogInformation($"User unlocked: {employeeId}");
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error unlocking user: {employeeId}");
                return false;
            }
        }
    }
}
