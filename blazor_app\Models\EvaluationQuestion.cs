using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace EmployeeRatingSystem.Blazor.Models
{
    /// <summary>
    /// Evaluation question model for configurable evaluation criteria.
    /// Equivalent to Django's EvaluationQuestion model.
    /// </summary>
    public class EvaluationQuestion : BaseModel
    {
        /// <summary>
        /// Category ID
        /// </summary>
        [Required]
        public int CategoryId { get; set; }

        /// <summary>
        /// Category navigation property
        /// </summary>
        public virtual EvaluationCategory Category { get; set; } = null!;

        /// <summary>
        /// Question text in English
        /// </summary>
        [Required]
        [StringLength(500)]
        [Display(Name = "English Text")]
        public string TextEn { get; set; } = string.Empty;

        /// <summary>
        /// Question text in Arabic
        /// </summary>
        [Required]
        [StringLength(500)]
        [Display(Name = "Arabic Text (النص بالعربية)")]
        public string TextAr { get; set; } = string.Empty;

        /// <summary>
        /// Help text in English
        /// </summary>
        [StringLength(1000)]
        [Display(Name = "English Help Text")]
        public string HelpTextEn { get; set; } = string.Empty;

        /// <summary>
        /// Help text in Arabic
        /// </summary>
        [StringLength(1000)]
        [Display(Name = "Arabic Help Text (نص المساعدة بالعربية)")]
        public string HelpTextAr { get; set; } = string.Empty;

        /// <summary>
        /// Weight percentage for this question within its category
        /// </summary>
        [Required]
        [Range(0.01, 100.00)]
        [Column(TypeName = "decimal(5,2)")]
        [Display(Name = "Weight Percentage")]
        public decimal WeightPercentage { get; set; }

        /// <summary>
        /// Whether this question is mandatory for all evaluations
        /// </summary>
        [Display(Name = "Is Mandatory")]
        public bool IsMandatory { get; set; } = true;

        /// <summary>
        /// Whether this question is currently active
        /// </summary>
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Display order for this question within its category
        /// </summary>
        [Required]
        [Display(Name = "Order")]
        public int Order { get; set; }

        /// <summary>
        /// Maximum score possible for this question
        /// </summary>
        [Required]
        [Range(1, 100)]
        [Column(TypeName = "decimal(4,1)")]
        [Display(Name = "Max Score")]
        public decimal MaxScore { get; set; } = 10.0m;

        /// <summary>
        /// Minimum score required for this question
        /// </summary>
        [Required]
        [Range(0, 100)]
        [Column(TypeName = "decimal(4,1)")]
        [Display(Name = "Min Score")]
        public decimal MinScore { get; set; } = 0.0m;

        /// <summary>
        /// Responses to this question
        /// </summary>
        public virtual ICollection<EvaluationResponse> Responses { get; set; } = new List<EvaluationResponse>();

        /// <summary>
        /// Get text based on language preference
        /// </summary>
        public string GetText(string language = "en")
        {
            return language == "ar" ? TextAr : TextEn;
        }

        /// <summary>
        /// Get help text based on language preference
        /// </summary>
        public string GetHelpText(string language = "en")
        {
            return language == "ar" ? HelpTextAr : HelpTextEn;
        }

        /// <summary>
        /// Calculate weighted score for a given raw score
        /// </summary>
        public decimal CalculateWeightedScore(decimal rawScore)
        {
            if (rawScore < MinScore || rawScore > MaxScore)
            {
                throw new ArgumentOutOfRangeException(nameof(rawScore), 
                    $"Score must be between {MinScore} and {MaxScore}");
            }

            // Normalize score to percentage and apply weight
            var percentage = (rawScore / MaxScore) * 100;
            return percentage * (WeightPercentage / 100);
        }

        /// <summary>
        /// Validate that the question configuration is valid
        /// </summary>
        public bool IsValid()
        {
            return WeightPercentage > 0 && 
                   WeightPercentage <= 100 && 
                   MaxScore > MinScore && 
                   MinScore >= 0 &&
                   !string.IsNullOrWhiteSpace(TextEn) &&
                   !string.IsNullOrWhiteSpace(TextAr);
        }

        public override string ToString()
        {
            return $"{TextEn} ({WeightPercentage}%)";
        }
    }
}
