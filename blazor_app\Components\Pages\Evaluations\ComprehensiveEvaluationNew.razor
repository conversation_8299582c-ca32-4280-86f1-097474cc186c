@page "/evaluations/comprehensive-new"
@page "/evaluations/comprehensive"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Components.Shared
@using EmployeeRatingSystem.Blazor.Services
@using EmployeeRatingSystem.Blazor.Models
@using EmployeeRatingSystem.Blazor.Data
@using Microsoft.EntityFrameworkCore
@inherits LocalizedComponentBase
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@inject IEvaluationCalculationService EvaluationCalculationService

<PageTitle>@L("Comprehensive Employee Evaluation", "التقييم الشامل للموظفين")</PageTitle>

<!-- Page Header -->
<div class="page-header @GetLayoutClass()">
    <nav aria-label="@L("Breadcrumb", "مسار التنقل")">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="/" class="text-decoration-none">@L("Home", "الرئيسية")</a>
            </li>
            <li class="breadcrumb-item">
                <a href="/evaluations" class="text-decoration-none">@L("Evaluations", "التقييمات")</a>
            </li>
            <li class="breadcrumb-item active" aria-current="page">
                @L("Comprehensive Evaluation", "التقييم الشامل")
            </li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-start flex-wrap">
        <div class="flex-grow-1">
            <h1 class="h1 mb-2">
                <i class="fas fa-calculator @GetMarginEnd() text-primary"></i>
                @L("Comprehensive Employee Evaluation", "التقييم الشامل للموظفين")
            </h1>
            <p class="lead mb-0">@L("Complete evaluation system with work volume, attendance, and manager assessment", "نظام التقييم الشامل مع حجم العمل والحضور وتقييم المدير")</p>
        </div>
    </div>
</div>

<!-- Evaluation Period Selection -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-calendar @GetMarginEnd(1)"></i>
            @L("Evaluation Period", "فترة التقييم")
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">@L("Year", "السنة")</label>
                <select class="form-select" @bind="selectedYear">
                    <option value="">@L("Select Year", "اختر السنة")</option>
                    @for (int year = DateTime.Now.Year; year >= DateTime.Now.Year - 5; year--)
                    {
                        <option value="@year.ToString()">@year</option>
                    }
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">@L("Month", "الشهر")</label>
                <select class="form-select" @bind="selectedMonth">
                    <option value="">@L("Select Month", "اختر الشهر")</option>
                    @for (int month = 1; month <= 12; month++)
                    {
                        <option value="@month.ToString()">@GetMonthName(month)</option>
                    }
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">@L("Department", "القسم")</label>
                <select class="form-select" @bind="selectedDepartmentId">
                    <option value="">@L("Select Department", "اختر القسم")</option>
                    @foreach (var dept in departments)
                    {
                        <option value="@dept.Id.ToString()">@GetDepartmentName(dept)</option>
                    }
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button class="btn btn-primary w-100" @onclick="LoadEmployees" disabled="@IsButtonDisabled()">
                    <i class="fas fa-search @GetMarginEnd(1)"></i>
                    @L("Load Employees", "تحميل الموظفين")
                </button>
            </div>
        </div>
    </div>
</div>



@if (employees.Any())
{
    <!-- Section 1: Work Volume Input Table -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-briefcase @GetMarginEnd(1)"></i>
                @L("Section 1: Work Volume Metrics", "القسم الأول: مقاييس حجم العمل")
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered mb-0" dir="@Direction">
                    <thead class="table-light">
                        <tr>
                            <th rowspan="2" class="text-center align-middle">@L("Employee", "الموظف")</th>
                            <th colspan="3" class="text-center">@L("Work Volume", "حجم العمل")</th>
                            <th rowspan="2" class="text-center align-middle">@L("Total", "المجموع")</th>
                            <th rowspan="2" class="text-center align-middle">@L("Percentage", "النسبة المئوية")</th>
                            <th rowspan="2" class="text-center align-middle">@L("60% Score", "درجة 60%")</th>
                        </tr>
                        <tr>
                            <th class="text-center">@L("Quality Program", "برنامج الجودة")</th>
                            <th class="text-center">@L("Oracle", "الأوراكل")</th>
                            <th class="text-center">@L("Documented Work", "العمل الموثق")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var employee in employees)
                        {
                            var workData = GetOrCreateWorkVolumeData(employee.Id);
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="profile-avatar profile-avatar-sm @GetMarginEnd(2)">
                                            @GetUserInitials(employee)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@GetUserDisplayName(employee)</div>
                                            <small class="text-muted">@employee.EmployeeId</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <input type="number" @bind="workData.QualityProgramWork" @bind:event="oninput"
                                           class="form-control form-control-sm text-center" step="0.01" min="0" />
                                </td>
                                <td class="text-center">
                                    <input type="number" @bind="workData.OracleWork" @bind:event="oninput"
                                           class="form-control form-control-sm text-center" step="0.01" min="0" />
                                </td>
                                <td class="text-center">
                                    <input type="number" @bind="workData.DocumentedWork" @bind:event="oninput"
                                           class="form-control form-control-sm text-center" step="0.01" min="0" />
                                </td>
                                <td class="text-center">
                                    <strong>@workData.TotalEmployeeWork.ToString("F2")</strong>
                                </td>
                                <td class="text-center">
                                    <strong>@GetWorkVolumePercentage(workData).ToString("P2")</strong>
                                </td>
                                <td class="text-center bg-info bg-opacity-25">
                                    <strong>@GetWorkVolumeScore(workData).ToString("F3")</strong>
                                </td>
                            </tr>
                        }
                        <!-- Department Totals Row -->
                        <tr class="table-warning">
                            <td><strong>@L("Department Totals", "مجاميع القسم")</strong></td>
                            <td class="text-center"><strong>@GetDepartmentQualityTotal().ToString("F2")</strong></td>
                            <td class="text-center"><strong>@GetDepartmentOracleTotal().ToString("F2")</strong></td>
                            <td class="text-center"><strong>@GetDepartmentDocumentedTotal().ToString("F2")</strong></td>
                            <td class="text-center"><strong>@GetDepartmentGrandTotal().ToString("F2")</strong></td>
                            <td class="text-center">-</td>
                            <td class="text-center">-</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Section 2: Attendance Data -->
    <div class="card mb-4">
        <div class="card-header bg-warning text-dark">
            <h5 class="card-title mb-0">
                <i class="fas fa-calendar-check @GetMarginEnd(1)"></i>
                @L("Attendance Metrics", "مقاييس الحضور")
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered mb-0" dir="@Direction">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">@L("Employee", "الموظف")</th>
                            <th class="text-center">@L("Monthly Working Days", "أيام العمل الشهرية")</th>
                            <th class="text-center">@L("Employee Attendance Days", "أيام حضور الموظف")</th>
                            <th class="text-center">@L("Attendance Percentage", "نسبة الحضور")</th>
                            <th class="text-center">@L("20% Attendance Score", "درجة الحضور 20%")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var employee in employees)
                        {
                            var attendanceData = GetOrCreateAttendanceData(employee.Id);
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="profile-avatar profile-avatar-sm @GetMarginEnd(2)">
                                            @GetUserInitials(employee)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@GetUserDisplayName(employee)</div>
                                            <small class="text-muted">@employee.EmployeeId</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <input type="number" @bind="attendanceData.TotalWorkingDays" @bind:event="oninput"
                                           class="form-control form-control-sm text-center" min="1" max="31" />
                                </td>
                                <td class="text-center">
                                    <input type="number" @bind="attendanceData.AttendanceDays" @bind:event="oninput"
                                           class="form-control form-control-sm text-center" min="0" max="31" />
                                </td>
                                <td class="text-center">
                                    <strong>@attendanceData.AttendancePercentage.ToString("P2")</strong>
                                </td>
                                <td class="text-center bg-warning bg-opacity-25">
                                    <strong>@GetAttendanceScore(attendanceData).ToString("F3")</strong>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Section 3: Manager Evaluation Criteria -->
    <div class="card mb-4">
        <div class="card-header bg-success text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-user-tie @GetMarginEnd(1)"></i>
                @L("Section 2: Manager Evaluation Criteria", "القسم الثاني: معايير تقييم المدير")
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered mb-0" dir="@Direction">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">@L("Employee", "الموظف")</th>
                            <th class="text-center">@L("Work Efficiency & Quality", "كفاءة العمل وجودته")</th>
                            <th class="text-center">@L("Leadership Ability", "القدرة على القيادة")</th>
                            <th class="text-center">@L("Planning & Development", "التخطيط والتطوير")</th>
                            <th class="text-center">@L("Teamwork", "العمل الجماعي")</th>
                            <th class="text-center">@L("Responsibility", "تحمل المسؤولية")</th>
                            <th class="text-center">@L("Emergency Management", "إدارة الطوارئ")</th>
                            <th class="text-center">@L("General Behavior", "السلوك العام")</th>
                            <th class="text-center">@L("Supervisor Relations", "العلاقة مع الرؤساء")</th>
                            <th class="text-center">@L("Discipline", "الانضباط")</th>
                            <th class="text-center">@L("Work Development", "تطوير العمل")</th>
                            <th class="text-center">@L("Total (50)", "المجموع (50)")</th>
                            <th class="text-center">@L("20% Manager Score", "درجة المدير 20%")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var employee in employees)
                        {
                            var supervisorEval = GetOrCreateSupervisorEvaluation(employee.Id);
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="profile-avatar profile-avatar-sm @GetMarginEnd(2)">
                                            @GetUserInitials(employee)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@GetUserDisplayName(employee)</div>
                                            <small class="text-muted">@employee.EmployeeId</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.EfficiencyAndQualityScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.LeadershipAbilityScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.PlanningAndInnovationScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.TeamworkParticipationScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.ResponsibilityAndPressureScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.EmergencyHandlingScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.GeneralBehaviorScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.RelationshipWithSuperiorsScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.DisciplineAndCommitmentScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <select class="form-select form-select-sm" @bind="supervisorEval.WorkDevelopmentScore">
                                        @for (int i = 0; i <= 5; i++)
                                        {
                                            <option value="@i">@i</option>
                                        }
                                    </select>
                                </td>
                                <td class="text-center">
                                    <strong>@supervisorEval.TotalScore.ToString("F1")</strong>
                                </td>
                                <td class="text-center bg-success bg-opacity-25">
                                    <strong>@supervisorEval.WeightedScore.ToString("F3")</strong>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Final Results Summary -->
    <div class="card mb-4">
        <div class="card-header bg-dark text-white">
            <h5 class="card-title mb-0">
                <i class="fas fa-trophy @GetMarginEnd(1)"></i>
                @L("Final Evaluation Results", "النتائج النهائية للتقييم")
            </h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered mb-0" dir="@Direction">
                    <thead class="table-light">
                        <tr>
                            <th class="text-center">@L("Employee", "الموظف")</th>
                            <th class="text-center">@L("Work Volume Score (60%)", "درجة حجم العمل (60%)")</th>
                            <th class="text-center">@L("Attendance Score (20%)", "درجة الحضور (20%)")</th>
                            <th class="text-center">@L("Manager Score (20%)", "درجة المدير (20%)")</th>
                            <th class="text-center">@L("Total Score (100%)", "الدرجة الإجمالية (100%)")</th>
                            <th class="text-center">@L("Rank", "الترتيب")</th>
                        </tr>
                    </thead>
                    <tbody>
                        @{
                            var rankedEmployees = employees
                                .Select(emp => new {
                                    Employee = emp,
                                    WorkScore = GetWorkVolumeScore(GetOrCreateWorkVolumeData(emp.Id)),
                                    AttendanceScore = GetAttendanceScore(GetOrCreateAttendanceData(emp.Id)),
                                    ManagerScore = GetOrCreateSupervisorEvaluation(emp.Id).WeightedScore,
                                    TotalScore = GetWorkVolumeScore(GetOrCreateWorkVolumeData(emp.Id)) +
                                               GetAttendanceScore(GetOrCreateAttendanceData(emp.Id)) +
                                               GetOrCreateSupervisorEvaluation(emp.Id).WeightedScore
                                })
                                .OrderByDescending(x => x.TotalScore)
                                .Select((x, index) => new { x.Employee, x.WorkScore, x.AttendanceScore, x.ManagerScore, x.TotalScore, Rank = index + 1 })
                                .ToList();
                        }
                        @foreach (var result in rankedEmployees)
                        {
                            <tr class="@(result.Rank <= 3 ? "table-success" : "")">
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="profile-avatar profile-avatar-sm @GetMarginEnd(2)">
                                            @GetUserInitials(result.Employee)
                                        </div>
                                        <div>
                                            <div class="fw-bold">@GetUserDisplayName(result.Employee)</div>
                                            <small class="text-muted">@result.Employee.EmployeeId</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="text-center">@result.WorkScore.ToString("F3")</td>
                                <td class="text-center">@result.AttendanceScore.ToString("F3")</td>
                                <td class="text-center">@result.ManagerScore.ToString("F3")</td>
                                <td class="text-center">
                                    <strong class="@(result.Rank <= 3 ? "text-success" : "")">
                                        @result.TotalScore.ToString("F3")
                                    </strong>
                                </td>
                                <td class="text-center">
                                    <span class="badge @(result.Rank == 1 ? "bg-warning" : result.Rank <= 3 ? "bg-success" : "bg-secondary")">
                                        @result.Rank
                                    </span>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Save Button -->
    <div class="d-flex justify-content-center mb-4">
        <button class="btn btn-primary btn-lg" @onclick="SaveEvaluationData" disabled="@(!employees.Any())">
            <i class="fas fa-save @GetMarginEnd(1)"></i>
            @L("Save Evaluation Data", "حفظ بيانات التقييم")
        </button>
    </div>
}

@code {
    private string selectedYear = "";
    private string selectedMonth = "";
    private string selectedDepartmentId = "";
    
    private List<Department> departments = new();
    private List<ApplicationUser> employees = new();
    private List<WorkVolumeData> workVolumeDataList = new();
    private List<AttendanceData> attendanceDataList = new();
    private List<SupervisorEvaluation> supervisorEvaluations = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadDepartments();
    }

    private bool IsButtonDisabled()
    {
        var disabled = string.IsNullOrEmpty(selectedYear) || string.IsNullOrEmpty(selectedMonth) || string.IsNullOrEmpty(selectedDepartmentId);
        Console.WriteLine($"Button disabled check - Year: '{selectedYear}', Month: '{selectedMonth}', Dept: '{selectedDepartmentId}', Disabled: {disabled}");
        return disabled;
    }

    private async Task LoadDepartments()
    {
        try
        {
            departments = await DbContext.Departments
                .Where(d => d.IsActive && !d.IsDeleted)
                .OrderBy(d => d.NameEn)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            // Log error without JavaScript interop during initialization
            Console.WriteLine($"Error loading departments: {ex.Message}");
        }
    }

    private async Task LoadEmployees()
    {
        try
        {
            Console.WriteLine($"LoadEmployees called - Year: '{selectedYear}', Month: '{selectedMonth}', Dept: '{selectedDepartmentId}'");

            if (string.IsNullOrEmpty(selectedDepartmentId))
            {
                Console.WriteLine("Department ID is empty, returning");
                return;
            }

            var departmentId = int.Parse(selectedDepartmentId);
            Console.WriteLine($"Parsed department ID: {departmentId}");

            // First try without filters to see if there are any users
            var allUsersInDept = await DbContext.Users
                .Where(u => u.PrimaryDepartmentId == departmentId)
                .ToListAsync();

            // Now apply filters
            employees = allUsersInDept
                .Where(u => u.IsActive && !u.IsDeleted)
                .OrderBy(u => u.EnglishName)
                .ToList();

            Console.WriteLine($"Found {allUsersInDept.Count} total users, {employees.Count} active employees");

        // Initialize data collections
        workVolumeDataList.Clear();
        attendanceDataList.Clear();
        supervisorEvaluations.Clear();

        var evaluationPeriod = $"{selectedYear}-{selectedMonth:D2}";

        // Load existing data or create new entries
        foreach (var employee in employees)
        {
            // Work Volume Data
            var workData = await DbContext.WorkVolumeData
                .FirstOrDefaultAsync(w => w.EmployeeId == employee.Id && w.EvaluationPeriod == evaluationPeriod);
            if (workData == null)
            {
                workData = new WorkVolumeData
                {
                    EmployeeId = employee.Id,
                    DepartmentId = departmentId,
                    EvaluationPeriod = evaluationPeriod,
                    RecordedBy = "System" // Should be current user
                };
            }
            workVolumeDataList.Add(workData);

            // Attendance Data
            var attendanceData = await DbContext.AttendanceData
                .FirstOrDefaultAsync(a => a.EmployeeId == employee.Id && a.EvaluationPeriod == evaluationPeriod);
            if (attendanceData == null)
            {
                attendanceData = new AttendanceData
                {
                    EmployeeId = employee.Id,
                    DepartmentId = departmentId,
                    EvaluationPeriod = evaluationPeriod,
                    TotalWorkingDays = GetWorkingDaysInMonth(int.Parse(selectedYear), int.Parse(selectedMonth)),
                    RecordedBy = "System" // Should be current user
                };
            }
            attendanceDataList.Add(attendanceData);

            // Supervisor Evaluation
            var supervisorEval = await DbContext.SupervisorEvaluations
                .FirstOrDefaultAsync(s => s.EmployeeId == employee.Id && s.EvaluationPeriod == evaluationPeriod);
            if (supervisorEval == null)
            {
                supervisorEval = new SupervisorEvaluation
                {
                    EmployeeId = employee.Id,
                    DepartmentId = departmentId,
                    EvaluationPeriod = evaluationPeriod,
                    SupervisorId = "System" // Should be current user
                };
            }
            supervisorEvaluations.Add(supervisorEval);
        }

        Console.WriteLine($"LoadEmployees completed. Final employee count: {employees.Count}");
        StateHasChanged();
        }
        catch (Exception ex)
        {
            // Log error and could show a user-friendly error message here
            Console.WriteLine($"Error loading employees: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }

    // Helper methods for data access
    private WorkVolumeData GetOrCreateWorkVolumeData(string employeeId)
    {
        return workVolumeDataList.FirstOrDefault(w => w.EmployeeId == employeeId) ?? new WorkVolumeData();
    }

    private AttendanceData GetOrCreateAttendanceData(string employeeId)
    {
        return attendanceDataList.FirstOrDefault(a => a.EmployeeId == employeeId) ?? new AttendanceData();
    }

    private SupervisorEvaluation GetOrCreateSupervisorEvaluation(string employeeId)
    {
        return supervisorEvaluations.FirstOrDefault(s => s.EmployeeId == employeeId) ?? new SupervisorEvaluation();
    }

    // Calculation methods
    private decimal GetWorkVolumePercentage(WorkVolumeData workData)
    {
        var departmentTotal = GetDepartmentGrandTotal();
        return departmentTotal > 0 ? workData.TotalEmployeeWork / departmentTotal : 0;
    }

    private decimal GetWorkVolumeScore(WorkVolumeData workData)
    {
        var percentage = GetWorkVolumePercentage(workData);
        var highestPercentage = GetHighestWorkVolumePercentage();
        return highestPercentage > 0 ? (percentage / highestPercentage) * 0.6m : 0;
    }

    private decimal GetHighestWorkVolumePercentage()
    {
        return workVolumeDataList.Any() ? workVolumeDataList.Max(w => GetWorkVolumePercentage(w)) : 0;
    }

    private decimal GetAttendanceScore(AttendanceData attendanceData)
    {
        var highestAttendanceRate = GetHighestAttendanceRate();
        return highestAttendanceRate > 0 ? (attendanceData.AttendancePercentage / highestAttendanceRate) * 0.2m : 0;
    }

    private decimal GetHighestAttendanceRate()
    {
        return attendanceDataList.Any() ? attendanceDataList.Max(a => a.AttendancePercentage) : 0;
    }

    // Department totals
    private decimal GetDepartmentQualityTotal() => workVolumeDataList.Sum(w => w.QualityProgramWork);
    private decimal GetDepartmentOracleTotal() => workVolumeDataList.Sum(w => w.OracleWork);
    private decimal GetDepartmentDocumentedTotal() => workVolumeDataList.Sum(w => w.DocumentedWork);
    private decimal GetDepartmentGrandTotal() => GetDepartmentQualityTotal() + GetDepartmentOracleTotal() + GetDepartmentDocumentedTotal();

    // Event handlers
    private void CalculateWorkVolumeScores()
    {
        StateHasChanged();
    }

    private void CalculateAttendanceScores()
    {
        StateHasChanged();
    }

    private void CalculateManagerScores()
    {
        StateHasChanged();
    }

    private async Task SaveEvaluationData()
    {
        try
        {
            // Save Work Volume Data
            foreach (var workData in workVolumeDataList)
            {
                var existing = await DbContext.WorkVolumeData
                    .FirstOrDefaultAsync(w => w.EmployeeId == workData.EmployeeId && w.EvaluationPeriod == workData.EvaluationPeriod);

                if (existing != null)
                {
                    existing.QualityProgramWork = workData.QualityProgramWork;
                    existing.OracleWork = workData.OracleWork;
                    existing.DocumentedWork = workData.DocumentedWork;
                    existing.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    workData.CreatedAt = DateTime.UtcNow;
                    workData.UpdatedAt = DateTime.UtcNow;
                    DbContext.WorkVolumeData.Add(workData);
                }
            }

            // Save Attendance Data
            foreach (var attendanceData in attendanceDataList)
            {
                var existing = await DbContext.AttendanceData
                    .FirstOrDefaultAsync(a => a.EmployeeId == attendanceData.EmployeeId && a.EvaluationPeriod == attendanceData.EvaluationPeriod);

                if (existing != null)
                {
                    existing.TotalWorkingDays = attendanceData.TotalWorkingDays;
                    existing.AttendanceDays = attendanceData.AttendanceDays;
                    existing.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    attendanceData.CreatedAt = DateTime.UtcNow;
                    attendanceData.UpdatedAt = DateTime.UtcNow;
                    DbContext.AttendanceData.Add(attendanceData);
                }
            }

            // Save Supervisor Evaluations
            foreach (var supervisorEval in supervisorEvaluations)
            {
                var existing = await DbContext.SupervisorEvaluations
                    .FirstOrDefaultAsync(s => s.EmployeeId == supervisorEval.EmployeeId && s.EvaluationPeriod == supervisorEval.EvaluationPeriod);

                if (existing != null)
                {
                    existing.EfficiencyAndQualityScore = supervisorEval.EfficiencyAndQualityScore;
                    existing.LeadershipAbilityScore = supervisorEval.LeadershipAbilityScore;
                    existing.PlanningAndInnovationScore = supervisorEval.PlanningAndInnovationScore;
                    existing.TeamworkParticipationScore = supervisorEval.TeamworkParticipationScore;
                    existing.ResponsibilityAndPressureScore = supervisorEval.ResponsibilityAndPressureScore;
                    existing.EmergencyHandlingScore = supervisorEval.EmergencyHandlingScore;
                    existing.GeneralBehaviorScore = supervisorEval.GeneralBehaviorScore;
                    existing.RelationshipWithSuperiorsScore = supervisorEval.RelationshipWithSuperiorsScore;
                    existing.DisciplineAndCommitmentScore = supervisorEval.DisciplineAndCommitmentScore;
                    existing.WorkDevelopmentScore = supervisorEval.WorkDevelopmentScore;
                    existing.UpdatedAt = DateTime.UtcNow;
                }
                else
                {
                    supervisorEval.CreatedAt = DateTime.UtcNow;
                    supervisorEval.UpdatedAt = DateTime.UtcNow;
                    DbContext.SupervisorEvaluations.Add(supervisorEval);
                }
            }

            await DbContext.SaveChangesAsync();

            // Calculate comprehensive evaluations
            var evaluationPeriod = $"{selectedYear}-{selectedMonth:D2}";
            await EvaluationCalculationService.CalculateDepartmentEvaluationsAsync(int.Parse(selectedDepartmentId), evaluationPeriod);

            // Show success message (could be replaced with a proper notification system)
            Console.WriteLine("Evaluation data saved successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving evaluation data: {ex.Message}");
            // Show error message (could be replaced with a proper notification system)
        }
    }

    // Helper methods
    private string GetMonthName(int month)
    {
        var monthNames = IsArabic 
            ? new[] { "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو", "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر" }
            : new[] { "January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December" };
        return monthNames[month - 1];
    }

    private string GetDepartmentName(Department dept) => IsArabic ? dept.NameAr : dept.NameEn;
    private string GetUserDisplayName(ApplicationUser user) => IsArabic ? user.ArabicName : user.EnglishName;
    private string GetUserInitials(ApplicationUser user) => string.Join("", user.EnglishName.Split(' ').Select(n => n.FirstOrDefault()));

    private int GetWorkingDaysInMonth(int year, int month)
    {
        var daysInMonth = DateTime.DaysInMonth(year, month);
        var workingDays = 0;
        for (int day = 1; day <= daysInMonth; day++)
        {
            var date = new DateTime(year, month, day);
            if (date.DayOfWeek != DayOfWeek.Friday && date.DayOfWeek != DayOfWeek.Saturday)
                workingDays++;
        }
        return workingDays;
    }
}
