using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace EmployeeRatingSystem.Blazor.Services
{
    public class DashboardStatisticsService
    {
        private readonly ApplicationDbContext _context;
        private readonly ILogger<DashboardStatisticsService> _logger;

        public DashboardStatisticsService(ApplicationDbContext context, ILogger<DashboardStatisticsService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<DashboardStatistics> GetStatisticsForUserAsync(ApplicationUser user)
        {
            try
            {
                // Simplified implementation to avoid startup issues
                _logger.LogInformation("Getting dashboard statistics for user {UserId} with role {Role}", user.Id, user.Role);

                return user.Role switch
                {
                    UserRole.SUPER_ADMIN => await GetSuperAdminStatisticsAsync(),
                    UserRole.MANAGER => await GetManagerStatisticsAsync(user.Id),
                    UserRole.SUPERVISOR => await GetSupervisorStatisticsAsync(user.Id),
                    UserRole.EMPLOYEE => await GetEmployeeStatisticsAsync(user.Id),
                    _ => new DashboardStatistics()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting dashboard statistics for user {UserId}", user.Id);
                return new DashboardStatistics();
            }
        }

        private async Task<DashboardStatistics> GetSuperAdminStatisticsAsync()
        {
            var totalUsers = await _context.Users.CountAsync(u => u.IsActive && !u.IsDeleted);
            var totalEmployees = await _context.Users.CountAsync(u => u.Role == UserRole.EMPLOYEE && u.IsActive && !u.IsDeleted);
            var totalManagers = await _context.Users.CountAsync(u => u.Role == UserRole.MANAGER && u.IsActive && !u.IsDeleted);
            var totalDepartments = await _context.Departments.CountAsync(d => d.IsActive && !d.IsDeleted);
            
            var pendingEvaluations = await _context.Evaluations.CountAsync(e => e.Status == EvaluationStatus.DRAFT || e.Status == EvaluationStatus.SUBMITTED);
            var completedEvaluations = await _context.Evaluations.CountAsync(e => e.Status == EvaluationStatus.APPROVED);
            var totalEvaluations = await _context.Evaluations.CountAsync();
            
            var averageScore = await _context.Evaluations
                .Where(e => e.Status == EvaluationStatus.APPROVED && e.PercentageScore.HasValue)
                .AverageAsync(e => (double?)e.PercentageScore) ?? 0;

            var activeDepartments = await _context.Departments
                .Where(d => d.IsActive && !d.IsDeleted)
                .CountAsync(d => d.PrimaryUsers.Any(u => u.IsActive && !u.IsDeleted));

            return new DashboardStatistics
            {
                TotalUsers = totalUsers,
                TotalEmployees = totalEmployees,
                TotalManagers = totalManagers,
                TotalDepartments = totalDepartments,
                PendingEvaluations = pendingEvaluations,
                CompletedEvaluations = completedEvaluations,
                TotalEvaluations = totalEvaluations,
                AveragePerformanceScore = Math.Round(averageScore, 1),
                ActiveDepartments = activeDepartments,
                CompletionRate = totalEvaluations > 0 ? Math.Round((double)completedEvaluations / totalEvaluations * 100, 1) : 0
            };
        }

        private async Task<DashboardStatistics> GetManagerStatisticsAsync(string managerId)
        {
            // Get departments managed by this manager
            var managedDepartments = await _context.Departments
                .Where(d => d.Managers.Any(m => m.Id == managerId) && d.IsActive && !d.IsDeleted)
                .ToListAsync();

            var departmentIds = managedDepartments.Select(d => d.Id).ToList();
            
            var teamSize = await _context.Users
                .CountAsync(u => u.PrimaryDepartmentId.HasValue && departmentIds.Contains(u.PrimaryDepartmentId.Value) 
                                && u.IsActive && !u.IsDeleted && u.Role == UserRole.EMPLOYEE);

            var pendingEvaluations = await _context.Evaluations
                .CountAsync(e => e.Employee.PrimaryDepartmentId.HasValue && departmentIds.Contains(e.Employee.PrimaryDepartmentId.Value)
                                && (e.Status == EvaluationStatus.SUBMITTED));

            var completedEvaluations = await _context.Evaluations
                .CountAsync(e => e.Employee.PrimaryDepartmentId.HasValue && departmentIds.Contains(e.Employee.PrimaryDepartmentId.Value)
                                && e.Status == EvaluationStatus.APPROVED);

            var totalEvaluations = await _context.Evaluations
                .CountAsync(e => e.Employee.PrimaryDepartmentId.HasValue && departmentIds.Contains(e.Employee.PrimaryDepartmentId.Value));

            var averageScore = await _context.Evaluations
                .Where(e => e.Employee.PrimaryDepartmentId.HasValue && departmentIds.Contains(e.Employee.PrimaryDepartmentId.Value)
                           && e.Status == EvaluationStatus.APPROVED && e.PercentageScore.HasValue)
                .AverageAsync(e => (double?)e.PercentageScore) ?? 0;

            return new DashboardStatistics
            {
                TeamSize = teamSize,
                PendingEvaluations = pendingEvaluations,
                CompletedEvaluations = completedEvaluations,
                TotalEvaluations = totalEvaluations,
                AveragePerformanceScore = Math.Round(averageScore, 1),
                CompletionRate = totalEvaluations > 0 ? Math.Round((double)completedEvaluations / totalEvaluations * 100, 1) : 0,
                ManagedDepartments = managedDepartments.Count
            };
        }

        private async Task<DashboardStatistics> GetSupervisorStatisticsAsync(string supervisorId)
        {
            // Get evaluations assigned to this supervisor
            var assignedEvaluations = await _context.Evaluations
                .CountAsync(e => e.EvaluatorId == supervisorId);

            var pendingEvaluations = await _context.Evaluations
                .CountAsync(e => e.EvaluatorId == supervisorId && e.Status == EvaluationStatus.DRAFT);

            var completedEvaluations = await _context.Evaluations
                .CountAsync(e => e.EvaluatorId == supervisorId && e.Status == EvaluationStatus.APPROVED);

            var overdueEvaluations = await _context.Evaluations
                .CountAsync(e => e.EvaluatorId == supervisorId
                                && e.Status == EvaluationStatus.DRAFT
                                && e.EvaluationPeriodEnd < DateTime.Now);

            var averageScore = await _context.Evaluations
                .Where(e => e.EvaluatorId == supervisorId && e.Status == EvaluationStatus.APPROVED && e.PercentageScore.HasValue)
                .AverageAsync(e => (double?)e.PercentageScore) ?? 0;

            var recentActivity = await _context.Evaluations
                .CountAsync(e => e.EvaluatorId == supervisorId
                                && e.UpdatedAt >= DateTime.Now.AddDays(-7));

            return new DashboardStatistics
            {
                AssignedEvaluations = assignedEvaluations,
                PendingEvaluations = pendingEvaluations,
                CompletedEvaluations = completedEvaluations,
                OverdueEvaluations = overdueEvaluations,
                AveragePerformanceScore = Math.Round(averageScore, 1),
                RecentActivity = recentActivity,
                CompletionRate = assignedEvaluations > 0 ? Math.Round((double)completedEvaluations / assignedEvaluations * 100, 1) : 0
            };
        }

        private async Task<DashboardStatistics> GetEmployeeStatisticsAsync(string employeeId)
        {
            var latestEvaluation = await _context.Evaluations
                .Where(e => e.EmployeeId == employeeId && e.Status == EvaluationStatus.APPROVED)
                .OrderByDescending(e => e.EvaluationPeriodEnd)
                .FirstOrDefaultAsync();

            var totalEvaluations = await _context.Evaluations
                .CountAsync(e => e.EmployeeId == employeeId);

            var averageScore = await _context.Evaluations
                .Where(e => e.EmployeeId == employeeId && e.Status == EvaluationStatus.APPROVED && e.PercentageScore.HasValue)
                .AverageAsync(e => (double?)e.PercentageScore) ?? 0;

            // Get department ranking (simplified)
            var employee = await _context.Users
                .Include(u => u.PrimaryDepartment)
                .FirstOrDefaultAsync(u => u.Id == employeeId);

            var departmentRanking = 0;
            if (employee?.PrimaryDepartmentId.HasValue == true)
            {
                var departmentEmployees = await _context.Evaluations
                    .Where(e => e.Employee.PrimaryDepartmentId == employee.PrimaryDepartmentId 
                               && e.Status == EvaluationStatus.APPROVED && e.PercentageScore.HasValue)
                    .GroupBy(e => e.EmployeeId)
                    .Select(g => new { EmployeeId = g.Key, AvgScore = g.Average(e => e.PercentageScore) })
                    .OrderByDescending(x => x.AvgScore)
                    .ToListAsync();

                departmentRanking = departmentEmployees.FindIndex(x => x.EmployeeId == employeeId) + 1;
            }

            var nextReviewDate = await _context.Evaluations
                .Where(e => e.EmployeeId == employeeId && e.Status == EvaluationStatus.DRAFT)
                .Select(e => (DateTime?)e.EvaluationPeriodEnd)
                .FirstOrDefaultAsync();

            return new DashboardStatistics
            {
                LatestScore = latestEvaluation?.PercentageScore ?? 0,
                AveragePerformanceScore = Math.Round(averageScore, 1),
                TotalEvaluations = totalEvaluations,
                DepartmentRanking = departmentRanking,
                NextReviewDate = nextReviewDate,
                DaysUntilNextReview = nextReviewDate.HasValue ? Math.Max(0, (nextReviewDate.Value - DateTime.Now).Days) : 0
            };
        }

        public async Task<List<RecentActivity>> GetRecentActivitiesAsync(string userId, UserRole userRole, int count = 5)
        {
            try
            {
                var activities = new List<RecentActivity>();

                // Get recent evaluations based on user role
                var recentEvaluations = userRole switch
                {
                    UserRole.SUPER_ADMIN => await _context.Evaluations
                        .Include(e => e.Employee)
                        .Include(e => e.Evaluator)
                        .OrderByDescending(e => e.UpdatedAt)
                        .Take(count)
                        .ToListAsync(),
                    
                    UserRole.MANAGER => await _context.Evaluations
                        .Include(e => e.Employee)
                            .ThenInclude(u => u.PrimaryDepartment)
                        .Include(e => e.Evaluator)
                        .Where(e => e.Employee.PrimaryDepartment != null &&
                                    e.Employee.PrimaryDepartment.Managers.Any(m => m.Id == userId))
                        .OrderByDescending(e => e.UpdatedAt)
                        .Take(count)
                        .ToListAsync(),
                    
                    UserRole.SUPERVISOR => await _context.Evaluations
                        .Include(e => e.Employee)
                        .Include(e => e.Evaluator)
                        .Where(e => e.EvaluatorId == userId)
                        .OrderByDescending(e => e.UpdatedAt)
                        .Take(count)
                        .ToListAsync(),
                    
                    UserRole.EMPLOYEE => await _context.Evaluations
                        .Include(e => e.Employee)
                        .Include(e => e.Evaluator)
                        .Where(e => e.EmployeeId == userId)
                        .OrderByDescending(e => e.UpdatedAt)
                        .Take(count)
                        .ToListAsync(),
                    
                    _ => new List<Evaluation>()
                };

                foreach (var evaluation in recentEvaluations)
                {
                    activities.Add(new RecentActivity
                    {
                        Id = evaluation.Id,
                        Type = "evaluation",
                        Title = $"Evaluation {evaluation.Status}",
                        Description = $"{evaluation.Employee?.EnglishName} - {evaluation.Status}",
                        Timestamp = evaluation.UpdatedAt,
                        Icon = GetStatusIcon(evaluation.Status),
                        Color = GetStatusColor(evaluation.Status)
                    });
                }

                return activities;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent activities for user {UserId}", userId);
                return new List<RecentActivity>();
            }
        }

        private string GetStatusIcon(EvaluationStatus status)
        {
            return status switch
            {
                EvaluationStatus.DRAFT => "fas fa-edit",
                EvaluationStatus.SUBMITTED => "fas fa-paper-plane",
                EvaluationStatus.APPROVED => "fas fa-check-circle",
                EvaluationStatus.REJECTED => "fas fa-times-circle",
                _ => "fas fa-file"
            };
        }

        private string GetStatusColor(EvaluationStatus status)
        {
            return status switch
            {
                EvaluationStatus.DRAFT => "warning",
                EvaluationStatus.SUBMITTED => "info",
                EvaluationStatus.APPROVED => "success",
                EvaluationStatus.REJECTED => "danger",
                _ => "secondary"
            };
        }
    }

    public class DashboardStatistics
    {
        // Super Admin Statistics
        public int TotalUsers { get; set; }
        public int TotalEmployees { get; set; }
        public int TotalManagers { get; set; }
        public int TotalDepartments { get; set; }
        public int ActiveDepartments { get; set; }

        // Manager Statistics
        public int TeamSize { get; set; }
        public int ManagedDepartments { get; set; }

        // Supervisor Statistics
        public int AssignedEvaluations { get; set; }
        public int OverdueEvaluations { get; set; }
        public int RecentActivity { get; set; }

        // Employee Statistics
        public decimal LatestScore { get; set; }
        public int DepartmentRanking { get; set; }
        public DateTime? NextReviewDate { get; set; }
        public int DaysUntilNextReview { get; set; }

        // Common Statistics
        public int PendingEvaluations { get; set; }
        public int CompletedEvaluations { get; set; }
        public int TotalEvaluations { get; set; }
        public double AveragePerformanceScore { get; set; }
        public double CompletionRate { get; set; }
    }

    public class RecentActivity
    {
        public int Id { get; set; }
        public string Type { get; set; } = "";
        public string Title { get; set; } = "";
        public string Description { get; set; } = "";
        public DateTime Timestamp { get; set; }
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "";
    }
}
