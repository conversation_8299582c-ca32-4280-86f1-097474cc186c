using EmployeeRatingSystem.Blazor.Data;
using EmployeeRatingSystem.Blazor.Models;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

namespace EmployeeRatingSystem.Blazor.Services
{
    /// <summary>
    /// Service for seeding initial data into the database
    /// </summary>
    public interface IDataSeedingService
    {
        Task SeedAsync();
        Task SeedRolesAsync();
        Task SeedDepartmentsAsync();
        Task SeedUsersAsync();
        Task SeedEvaluationCategoriesAsync();
        Task SeedEvaluationQuestionsAsync();
        Task SeedSampleEvaluationsAsync();
    }

    public class DataSeedingService : IDataSeedingService
    {
        private readonly ApplicationDbContext _context;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly ILogger<DataSeedingService> _logger;

        public DataSeedingService(
            ApplicationDbContext context,
            UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager,
            ILogger<DataSeedingService> logger)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
            _logger = logger;
        }

        public async Task SeedAsync()
        {
            try
            {
                _logger.LogInformation("Starting data seeding...");

                await SeedRolesAsync();
                await SeedDepartmentsAsync();
                await SeedUsersAsync();
                await SeedEvaluationCategoriesAsync();
                await SeedEvaluationQuestionsAsync();
                await SeedSampleEvaluationsAsync();

                _logger.LogInformation("Data seeding completed successfully.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred during data seeding.");
                throw;
            }
        }

        public async Task SeedRolesAsync()
        {
            var roles = new[]
            {
                "SUPER_ADMIN",
                "MANAGER",
                "SUPERVISOR",
                "EXCELLENCE_TEAM",
                "EMPLOYEE"
            };

            foreach (var roleName in roles)
            {
                if (!await _roleManager.RoleExistsAsync(roleName))
                {
                    await _roleManager.CreateAsync(new IdentityRole(roleName));
                    _logger.LogInformation($"Created role: {roleName}");
                }
            }
        }

        public async Task SeedDepartmentsAsync()
        {
            if (await _context.Departments.AnyAsync())
                return;

            var departments = new[]
            {
                new Department
                {
                    Code = "IT",
                    NameEn = "Information Technology",
                    NameAr = "تكنولوجيا المعلومات",
                    DescriptionEn = "Responsible for all IT infrastructure and software development",
                    DescriptionAr = "مسؤول عن جميع البنية التحتية لتكنولوجيا المعلومات وتطوير البرمجيات",
                    IsActive = true,
                    Order = 1
                },
                new Department
                {
                    Code = "HR",
                    NameEn = "Human Resources",
                    NameAr = "الموارد البشرية",
                    DescriptionEn = "Manages employee relations, recruitment, and HR policies",
                    DescriptionAr = "يدير علاقات الموظفين والتوظيف وسياسات الموارد البشرية",
                    IsActive = true,
                    Order = 2
                },
                new Department
                {
                    Code = "FIN",
                    NameEn = "Finance",
                    NameAr = "المالية",
                    DescriptionEn = "Handles financial planning, accounting, and budget management",
                    DescriptionAr = "يتعامل مع التخطيط المالي والمحاسبة وإدارة الميزانية",
                    IsActive = true,
                    Order = 3
                },
                new Department
                {
                    Code = "MKT",
                    NameEn = "Marketing",
                    NameAr = "التسويق",
                    DescriptionEn = "Responsible for marketing strategies and customer engagement",
                    DescriptionAr = "مسؤول عن استراتيجيات التسويق وإشراك العملاء",
                    IsActive = true,
                    Order = 4
                },
                new Department
                {
                    Code = "OPS",
                    NameEn = "Operations",
                    NameAr = "العمليات",
                    DescriptionEn = "Manages day-to-day operations and process optimization",
                    DescriptionAr = "يدير العمليات اليومية وتحسين العمليات",
                    IsActive = true,
                    Order = 5
                }
            };

            _context.Departments.AddRange(departments);
            await _context.SaveChangesAsync();
            _logger.LogInformation($"Seeded {departments.Length} departments.");
        }

        public async Task SeedUsersAsync()
        {
            // Force user creation for debugging - check if EMP001 exists specifically
            var emp001Exists = await _userManager.Users.AnyAsync(u => u.EmployeeId == "EMP001");
            if (emp001Exists)
            {
                _logger.LogInformation("EMP001 user already exists, skipping user seeding");
                return;
            }

            var itDepartment = await _context.Departments.FirstAsync(d => d.Code == "IT");
            var hrDepartment = await _context.Departments.FirstAsync(d => d.Code == "HR");

            var users = new[]
            {
                new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmployeeId = "EMP001",
                    EnglishName = "System Administrator",
                    ArabicName = "مدير النظام",
                    Role = UserRole.SUPER_ADMIN,
                    IsActive = true,
                    PrimaryDepartmentId = itDepartment.Id,
                    PreferredLanguage = "en"
                },
                new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmployeeId = "EMP002",
                    EnglishName = "Sarah Ahmed",
                    ArabicName = "سارة أحمد",
                    Role = UserRole.MANAGER,
                    IsActive = true,
                    PrimaryDepartmentId = hrDepartment.Id,
                    PreferredLanguage = "ar"
                },
                new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmployeeId = "EMP003",
                    EnglishName = "Mohamed Hassan",
                    ArabicName = "محمد حسن",
                    Role = UserRole.SUPERVISOR,
                    IsActive = true,
                    PrimaryDepartmentId = itDepartment.Id,
                    PreferredLanguage = "ar"
                },
                new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmployeeId = "EMP004",
                    EnglishName = "Ahmed Mohamed",
                    ArabicName = "أحمد محمد",
                    Role = UserRole.EMPLOYEE,
                    IsActive = true,
                    PrimaryDepartmentId = itDepartment.Id,
                    PreferredLanguage = "ar"
                },
                new ApplicationUser
                {
                    UserName = "<EMAIL>",
                    Email = "<EMAIL>",
                    EmployeeId = "EMP005",
                    EnglishName = "Fatima Ali",
                    ArabicName = "فاطمة علي",
                    Role = UserRole.EMPLOYEE,
                    IsActive = true,
                    PrimaryDepartmentId = hrDepartment.Id,
                    PreferredLanguage = "ar"
                }
            };

            foreach (var user in users)
            {
                var result = await _userManager.CreateAsync(user, "Password123!");
                if (result.Succeeded)
                {
                    await _userManager.AddToRoleAsync(user, user.Role.ToString());
                    _logger.LogInformation($"Created user: {user.EnglishName} ({user.Email})");
                }
                else
                {
                    _logger.LogError($"Failed to create user {user.Email}: {string.Join(", ", result.Errors.Select(e => e.Description))}");
                }
            }
        }

        public async Task SeedEvaluationCategoriesAsync()
        {
            if (await _context.EvaluationCategories.AnyAsync())
                return;

            var categories = new[]
            {
                new EvaluationCategory
                {
                    NameEn = "Technical Skills",
                    NameAr = "المهارات التقنية",
                    DescriptionEn = "Assessment of technical competencies and expertise",
                    DescriptionAr = "تقييم الكفاءات والخبرات التقنية",
                    WeightPercentage = 30,
                    MaxScore = 10,
                    MinScore = 0,
                    IsActive = true,
                    Order = 1,
                    ColorCode = "#3b82f6",
                    IconClass = "fas fa-code"
                },
                new EvaluationCategory
                {
                    NameEn = "Communication",
                    NameAr = "التواصل",
                    DescriptionEn = "Evaluation of communication and interpersonal skills",
                    DescriptionAr = "تقييم مهارات التواصل والعلاقات الشخصية",
                    WeightPercentage = 25,
                    MaxScore = 10,
                    MinScore = 0,
                    IsActive = true,
                    Order = 2,
                    ColorCode = "#10b981",
                    IconClass = "fas fa-comments"
                },
                new EvaluationCategory
                {
                    NameEn = "Leadership",
                    NameAr = "القيادة",
                    DescriptionEn = "Assessment of leadership and management capabilities",
                    DescriptionAr = "تقييم قدرات القيادة والإدارة",
                    WeightPercentage = 20,
                    MaxScore = 10,
                    MinScore = 0,
                    IsActive = true,
                    Order = 3,
                    ColorCode = "#f59e0b",
                    IconClass = "fas fa-crown"
                },
                new EvaluationCategory
                {
                    NameEn = "Problem Solving",
                    NameAr = "حل المشكلات",
                    DescriptionEn = "Evaluation of analytical and problem-solving abilities",
                    DescriptionAr = "تقييم القدرات التحليلية وحل المشكلات",
                    WeightPercentage = 15,
                    MaxScore = 10,
                    MinScore = 0,
                    IsActive = true,
                    Order = 4,
                    ColorCode = "#8b5cf6",
                    IconClass = "fas fa-lightbulb"
                },
                new EvaluationCategory
                {
                    NameEn = "Teamwork",
                    NameAr = "العمل الجماعي",
                    DescriptionEn = "Assessment of collaboration and team participation",
                    DescriptionAr = "تقييم التعاون والمشاركة في الفريق",
                    WeightPercentage = 10,
                    MaxScore = 10,
                    MinScore = 0,
                    IsActive = true,
                    Order = 5,
                    ColorCode = "#ef4444",
                    IconClass = "fas fa-users"
                }
            };

            _context.EvaluationCategories.AddRange(categories);
            await _context.SaveChangesAsync();
            _logger.LogInformation($"Seeded {categories.Length} evaluation categories.");
        }

        public async Task SeedEvaluationQuestionsAsync()
        {
            if (await _context.EvaluationQuestions.AnyAsync())
                return;

            var categories = await _context.EvaluationCategories.ToListAsync();
            var questions = new List<EvaluationQuestion>();

            // Technical Skills Questions
            var technicalCategory = categories.First(c => c.NameEn == "Technical Skills");
            questions.AddRange(new[]
            {
                new EvaluationQuestion
                {
                    CategoryId = technicalCategory.Id,
                    TextEn = "Demonstrates proficiency in required technical skills",
                    TextAr = "يظهر إتقاناً في المهارات التقنية المطلوبة",
                    HelpTextEn = "Evaluate the employee's mastery of job-specific technical competencies",
                    HelpTextAr = "قيم إتقان الموظف للكفاءات التقنية الخاصة بالوظيفة",
                    WeightPercentage = 40,
                    MaxScore = 10,
                    MinScore = 0,
                    IsActive = true,
                    Order = 1
                },
                new EvaluationQuestion
                {
                    CategoryId = technicalCategory.Id,
                    TextEn = "Stays updated with latest industry trends and technologies",
                    TextAr = "يبقى محدثاً بأحدث اتجاهات وتقنيات الصناعة",
                    HelpTextEn = "Assess the employee's commitment to continuous learning",
                    HelpTextAr = "قيم التزام الموظف بالتعلم المستمر",
                    WeightPercentage = 30,
                    MaxScore = 10,
                    MinScore = 0,
                    IsActive = true,
                    Order = 2
                },
                new EvaluationQuestion
                {
                    CategoryId = technicalCategory.Id,
                    TextEn = "Applies technical knowledge effectively to solve problems",
                    TextAr = "يطبق المعرفة التقنية بفعالية لحل المشكلات",
                    HelpTextEn = "Evaluate practical application of technical skills",
                    HelpTextAr = "قيم التطبيق العملي للمهارات التقنية",
                    WeightPercentage = 30,
                    MaxScore = 10,
                    MinScore = 0,
                    IsActive = true,
                    Order = 3
                }
            });

            // Communication Questions
            var communicationCategory = categories.First(c => c.NameEn == "Communication");
            questions.AddRange(new[]
            {
                new EvaluationQuestion
                {
                    CategoryId = communicationCategory.Id,
                    TextEn = "Communicates clearly and effectively with team members",
                    TextAr = "يتواصل بوضوح وفعالية مع أعضاء الفريق",
                    HelpTextEn = "Assess verbal and written communication skills",
                    HelpTextAr = "قيم مهارات التواصل الشفهي والكتابي",
                    WeightPercentage = 50,
                    MaxScore = 10,
                    MinScore = 0,
                    IsActive = true,
                    Order = 1
                },
                new EvaluationQuestion
                {
                    CategoryId = communicationCategory.Id,
                    TextEn = "Listens actively and responds appropriately",
                    TextAr = "يستمع بنشاط ويستجيب بشكل مناسب",
                    HelpTextEn = "Evaluate listening skills and responsiveness",
                    HelpTextAr = "قيم مهارات الاستماع والاستجابة",
                    WeightPercentage = 50,
                    MaxScore = 10,
                    MinScore = 0,
                    IsActive = true,
                    Order = 2
                }
            });

            _context.EvaluationQuestions.AddRange(questions);
            await _context.SaveChangesAsync();
            _logger.LogInformation($"Seeded {questions.Count} evaluation questions.");
        }

        public async Task SeedSampleEvaluationsAsync()
        {
            if (await _context.Evaluations.AnyAsync())
                return;

            try
            {
                // Get some users to create evaluations for
                var employees = await _context.Users
                    .Where(u => u.Role == UserRole.EMPLOYEE && u.IsActive && !u.IsDeleted)
                    .Take(5)
                    .ToListAsync();

                var evaluators = await _context.Users
                    .Where(u => (u.Role == UserRole.MANAGER || u.Role == UserRole.SUPER_ADMIN) && u.IsActive && !u.IsDeleted)
                    .Take(2)
                    .ToListAsync();

                if (!employees.Any() || !evaluators.Any())
                {
                    _logger.LogInformation("Not enough users found to create sample evaluations.");
                    return;
                }

                var random = new Random(42);
                var evaluations = new List<Evaluation>();

                foreach (var employee in employees)
                {
                    var evaluator = evaluators[random.Next(evaluators.Count)];
                    var startDate = DateTime.Now.AddDays(-random.Next(30, 90));
                    var endDate = startDate.AddDays(30);

                    var evaluation = new Evaluation
                    {
                        EmployeeId = employee.Id,
                        EvaluatorId = evaluator.Id,
                        EvaluationPeriodStart = startDate,
                        EvaluationPeriodEnd = endDate,
                        Status = (EvaluationStatus)random.Next(0, 4), // Random status
                        TotalScore = (decimal)(random.NextDouble() * 40 + 60), // 60-100
                        PercentageScore = (decimal)(random.NextDouble() * 40 + 60), // 60-100%
                        OverallCommentsEn = $"Sample evaluation for {employee.EnglishName}",
                        OverallCommentsAr = $"تقييم عينة لـ {employee.ArabicName}",
                        CreatedAt = startDate.AddDays(random.Next(1, 10)),
                        UpdatedAt = DateTime.UtcNow
                    };

                    if (evaluation.Status == EvaluationStatus.SUBMITTED || evaluation.Status == EvaluationStatus.APPROVED)
                    {
                        evaluation.SubmittedAt = evaluation.CreatedAt.AddDays(random.Next(1, 5));
                    }

                    if (evaluation.Status == EvaluationStatus.APPROVED)
                    {
                        evaluation.ApprovedAt = evaluation.SubmittedAt?.AddDays(random.Next(1, 3));
                        evaluation.ApprovedById = evaluator.Id;
                    }

                    evaluations.Add(evaluation);
                }

                _context.Evaluations.AddRange(evaluations);
                await _context.SaveChangesAsync();
                _logger.LogInformation($"Seeded {evaluations.Count} sample evaluations.");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error seeding sample evaluations.");
            }
        }
    }
}
