using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using EmployeeRatingSystem.Blazor.Models;

namespace EmployeeRatingSystem.Blazor.Data
{
    /// <summary>
    /// Application database context that extends IdentityDbContext.
    /// Equivalent to Django's database configuration with all models.
    /// </summary>
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // DbSets for all models
        public DbSet<Department> Departments { get; set; }
        public DbSet<UserDepartment> UserDepartments { get; set; }
        public DbSet<EmployeePreConfiguration> EmployeePreConfigurations { get; set; }
        public DbSet<EmployeePreConfigurationDepartment> EmployeePreConfigurationDepartments { get; set; }
        public DbSet<EvaluationCategory> EvaluationCategories { get; set; }
        public DbSet<EvaluationQuestion> EvaluationQuestions { get; set; }
        public DbSet<Evaluation> Evaluations { get; set; }
        public DbSet<EvaluationResponse> EvaluationResponses { get; set; }

        // Comprehensive evaluation system
        public DbSet<WorkVolumeData> WorkVolumeData { get; set; }
        public DbSet<DepartmentWorkVolumeTotal> DepartmentWorkVolumeTotals { get; set; }
        public DbSet<AttendanceData> AttendanceData { get; set; }
        public DbSet<MonthlyWorkingDays> MonthlyWorkingDays { get; set; }
        public DbSet<SupervisorEvaluation> SupervisorEvaluations { get; set; }
        public DbSet<ComprehensiveEvaluation> ComprehensiveEvaluations { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure ApplicationUser
            builder.Entity<ApplicationUser>(entity =>
            {
                entity.HasIndex(e => e.EmployeeId).IsUnique();
                entity.Property(e => e.EmployeeId).IsRequired().HasMaxLength(50);
                entity.Property(e => e.ArabicName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.EnglishName).IsRequired().HasMaxLength(255);
                entity.Property(e => e.PreferredLanguage).HasMaxLength(10).HasDefaultValue("en");

                // Configure relationships
                entity.HasOne(e => e.PrimaryDepartment)
                    .WithMany(d => d.PrimaryUsers)
                    .HasForeignKey(e => e.PrimaryDepartmentId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasMany(e => e.ManagedDepartments)
                    .WithMany(d => d.Managers)
                    .UsingEntity(j => j.ToTable("UserManagedDepartments"));
            });

            // Configure Department
            builder.Entity<Department>(entity =>
            {
                entity.HasIndex(e => e.Code).IsUnique();
                entity.Property(e => e.Code).IsRequired().HasMaxLength(50);
                entity.Property(e => e.NameEn).IsRequired().HasMaxLength(255);
                entity.Property(e => e.NameAr).IsRequired().HasMaxLength(255);

                // Self-referencing relationship for hierarchy
                entity.HasOne(e => e.Parent)
                    .WithMany(e => e.Children)
                    .HasForeignKey(e => e.ParentId)
                    .OnDelete(DeleteBehavior.Restrict);

                // Indexes for tree structure
                entity.HasIndex(e => new { e.TreeId, e.Lft });
                entity.HasIndex(e => new { e.TreeId, e.Rght });
            });

            // Configure UserDepartment
            builder.Entity<UserDepartment>(entity =>
            {
                entity.HasOne(e => e.User)
                    .WithMany(u => u.UserDepartments)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Department)
                    .WithMany(d => d.UserDepartments)
                    .HasForeignKey(e => e.DepartmentId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Unique constraint for user-department combination
                entity.HasIndex(e => new { e.UserId, e.DepartmentId }).IsUnique();
            });

            // Configure EvaluationCategory
            builder.Entity<EvaluationCategory>(entity =>
            {
                entity.Property(e => e.NameEn).IsRequired().HasMaxLength(255);
                entity.Property(e => e.NameAr).IsRequired().HasMaxLength(255);
                entity.Property(e => e.WeightPercentage).HasColumnType("decimal(5,2)");
                entity.Property(e => e.MaxScore).HasColumnType("decimal(4,1)");
                entity.Property(e => e.MinScore).HasColumnType("decimal(4,1)");
                entity.Property(e => e.ColorCode).HasMaxLength(7).HasDefaultValue("#3b82f6");
                entity.Property(e => e.IconClass).HasMaxLength(50).HasDefaultValue("fas fa-star");

                entity.HasIndex(e => e.Order);
            });

            // Configure EvaluationQuestion
            builder.Entity<EvaluationQuestion>(entity =>
            {
                entity.Property(e => e.TextEn).IsRequired().HasMaxLength(500);
                entity.Property(e => e.TextAr).IsRequired().HasMaxLength(500);
                entity.Property(e => e.HelpTextEn).HasMaxLength(1000);
                entity.Property(e => e.HelpTextAr).HasMaxLength(1000);
                entity.Property(e => e.WeightPercentage).HasColumnType("decimal(5,2)");
                entity.Property(e => e.MaxScore).HasColumnType("decimal(4,1)");
                entity.Property(e => e.MinScore).HasColumnType("decimal(4,1)");

                entity.HasOne(e => e.Category)
                    .WithMany(c => c.Questions)
                    .HasForeignKey(e => e.CategoryId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => new { e.CategoryId, e.Order });
            });

            // Configure Evaluation
            builder.Entity<Evaluation>(entity =>
            {
                entity.Property(e => e.TotalScore).HasColumnType("decimal(5,2)");
                entity.Property(e => e.PercentageScore).HasColumnType("decimal(5,2)");
                entity.Property(e => e.RejectionReason).HasMaxLength(1000);

                entity.HasOne(e => e.Employee)
                    .WithMany(u => u.Evaluations)
                    .HasForeignKey(e => e.EmployeeId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Evaluator)
                    .WithMany(u => u.ConductedEvaluations)
                    .HasForeignKey(e => e.EvaluatorId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ApprovedBy)
                    .WithMany(u => u.ApprovedEvaluations)
                    .HasForeignKey(e => e.ApprovedById)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.RejectedBy)
                    .WithMany()
                    .HasForeignKey(e => e.RejectedById)
                    .OnDelete(DeleteBehavior.SetNull);

                // Indexes for common queries
                entity.HasIndex(e => new { e.EmployeeId, e.EvaluationPeriodStart, e.EvaluationPeriodEnd });
                entity.HasIndex(e => e.Status);
            });

            // Configure EvaluationResponse
            builder.Entity<EvaluationResponse>(entity =>
            {
                entity.Property(e => e.Score).HasColumnType("decimal(4,1)");

                entity.HasOne(e => e.Evaluation)
                    .WithMany(ev => ev.Responses)
                    .HasForeignKey(e => e.EvaluationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Question)
                    .WithMany(q => q.Responses)
                    .HasForeignKey(e => e.QuestionId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Unique constraint for evaluation-question combination
                entity.HasIndex(e => new { e.EvaluationId, e.QuestionId }).IsUnique();
            });

            // Configure EmployeePreConfiguration
            builder.Entity<EmployeePreConfiguration>(entity =>
            {
                entity.HasIndex(e => e.EmployeeId).IsUnique();
                entity.Property(e => e.EmployeeId).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Notes).HasMaxLength(1000);

                entity.HasOne(e => e.PrimaryDepartment)
                    .WithMany()
                    .HasForeignKey(e => e.PrimaryDepartmentId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.CreatedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.UpdatedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.UpdatedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // Configure EmployeePreConfigurationDepartment
            builder.Entity<EmployeePreConfigurationDepartment>(entity =>
            {
                entity.HasOne(e => e.EmployeePreConfiguration)
                    .WithMany(pc => pc.AdditionalDepartments)
                    .HasForeignKey(e => e.EmployeePreConfigurationId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.Department)
                    .WithMany()
                    .HasForeignKey(e => e.DepartmentId)
                    .OnDelete(DeleteBehavior.Cascade);

                // Unique constraint for pre-configuration-department combination
                entity.HasIndex(e => new { e.EmployeePreConfigurationId, e.DepartmentId }).IsUnique();
            });

            // Configure enum conversions
            builder.Entity<ApplicationUser>()
                .Property(e => e.Role)
                .HasConversion<string>();

            builder.Entity<UserDepartment>()
                .Property(e => e.RoleInDepartment)
                .HasConversion<string>();

            builder.Entity<Evaluation>()
                .Property(e => e.Status)
                .HasConversion<string>();

            builder.Entity<EmployeePreConfiguration>()
                .Property(e => e.AssignedRole)
                .HasConversion<string>();

            builder.Entity<EmployeePreConfigurationDepartment>()
                .Property(e => e.RoleInDepartment)
                .HasConversion<string>();
        }

        /// <summary>
        /// Override SaveChanges to automatically update timestamps
        /// </summary>
        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        /// <summary>
        /// Override SaveChangesAsync to automatically update timestamps
        /// </summary>
        public override Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return base.SaveChangesAsync(cancellationToken);
        }

        /// <summary>
        /// Update timestamps for entities that inherit from TimeStampedModel
        /// </summary>
        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is TimeStampedModel && 
                           (e.State == EntityState.Added || e.State == EntityState.Modified));

            foreach (var entry in entries)
            {
                var entity = (TimeStampedModel)entry.Entity;
                
                if (entry.State == EntityState.Added)
                {
                    entity.CreatedAt = DateTime.UtcNow;
                }
                
                entity.UpdatedAt = DateTime.UtcNow;
            }
        }
    }
}
