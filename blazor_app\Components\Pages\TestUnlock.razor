@page "/test-unlock"
@rendermode InteractiveServer
@using EmployeeRatingSystem.Blazor.Services
@inject IEmployeeAuthenticationService EmployeeAuthService

<h3>Test Unlock</h3>

<div class="container">
    <button @onclick="UnlockTestUser" class="btn btn-primary">Unlock User 1234</button>
    <p>@message</p>
</div>

@code {
    private string message = "";

    private async Task UnlockTestUser()
    {
        try
        {
            var result = await EmployeeAuthService.UnlockUserAsync("1234");
            message = result ? "User unlocked successfully!" : "Failed to unlock user.";
        }
        catch (Exception ex)
        {
            message = $"Error: {ex.Message}";
        }
    }
}
