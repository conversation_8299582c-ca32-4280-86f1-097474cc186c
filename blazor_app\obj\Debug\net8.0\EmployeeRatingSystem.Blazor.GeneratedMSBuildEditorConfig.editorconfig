is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = EmployeeRatingSystem.Blazor
build_property.RootNamespace = EmployeeRatingSystem.Blazor
build_property.ProjectDir = C:\Users\<USER>\rating\blazor_app\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\rating\blazor_app
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/rating/blazor_app/Components/App.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Departments/DepartmentTreeNode.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xEZXBhcnRtZW50c1xEZXBhcnRtZW50VHJlZU5vZGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Layout/AuthLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcQXV0aExheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Account/Login.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBY2NvdW50XExvZ2luLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Account/Logout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBY2NvdW50XExvZ291dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Account/Profile.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBY2NvdW50XFByb2ZpbGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Account/Register.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBY2NvdW50XFJlZ2lzdGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Admin/UnlockAccount.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xBZG1pblxVbmxvY2tBY2NvdW50LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Counter.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xDb3VudGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Dashboard.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEYXNoYm9hcmQucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Departments/Create.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEZXBhcnRtZW50c1xDcmVhdGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Departments/Index.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xEZXBhcnRtZW50c1xJbmRleC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Error.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFcnJvci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Evaluations/ComprehensiveEvaluationNew.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFdmFsdWF0aW9uc1xDb21wcmVoZW5zaXZlRXZhbHVhdGlvbk5ldy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Evaluations/EvaluationTable.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFdmFsdWF0aW9uc1xFdmFsdWF0aW9uVGFibGUucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Evaluations/Index.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFdmFsdWF0aW9uc1xJbmRleC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Home.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/TestAuth.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xUZXN0QXV0aC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/TestLogin.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xUZXN0TG9naW4ucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/TestUnlock.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xUZXN0VW5sb2NrLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Users/<USER>
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xVc2Vyc1xJbmRleC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Pages/Weather.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXZWF0aGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Routes.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSb3V0ZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Shared/AuthenticationGuard.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaGFyZWRcQXV0aGVudGljYXRpb25HdWFyZC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Shared/DepartmentTreeNode.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaGFyZWRcRGVwYXJ0bWVudFRyZWVOb2RlLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Shared/LanguageSwitcher.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaGFyZWRcTGFuZ3VhZ2VTd2l0Y2hlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Shared/PageHeader.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTaGFyZWRcUGFnZUhlYWRlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xfSW1wb3J0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/rating/blazor_app/Components/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTWFpbkxheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-zzt0ru0its
